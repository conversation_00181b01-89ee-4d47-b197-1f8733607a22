"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Products = void 0;
const tslib_1 = require("tslib");
const striptags_1 = tslib_1.__importDefault(require("striptags"));
const providers_1 = require("@provider/providers");
const types_1 = require("./types");
class Products extends providers_1.Products {
    merchantId;
    constructor(provider, merchantId) {
        super(provider);
        this.merchantId = merchantId;
    }
    async get(shopId) {
        const { merchantId } = this;
        return this.API.getMenu(shopId || merchantId);
    }
    async update(menu, shopId) {
        const { merchantId } = this;
        await this.API.updateMenu(shopId || merchantId, menu);
    }
    async refresh(menu) {
        return this.update(menu);
    }
    /**
     * Transform bundleList from product to UberEats ModifierGroups
     */
    createModifierGroups(product, variant, language) {
        const modifierGroups = [];
        const modifierGroupIds = [];
        const modifierItems = [];
        // Check if product has bundleList
        if (!product.bundleList || product.bundleList.length === 0) {
            return { modifierGroups, modifierGroupIds, modifierItems };
        }
        // Transform each bundle to a ModifierGroup
        for (const bundle of product.bundleList) {
            const modifierGroupId = `${variant.id}-${bundle.key}`;
            modifierGroupIds.push(modifierGroupId);
            // Create modifier options from bundle values
            const modifierOptions = bundle.values.map((value) => ({
                id: `${variant.id}-${bundle.key}-${value.sku || value.title || 'option'}`,
                type: types_1.EntityType.ITEM
            }));
            // Create the ModifierGroup
            const modifierGroup = {
                id: modifierGroupId,
                title: {
                    translations: {
                        [language]: bundle.title || ''
                    }
                },
                external_data: bundle.key,
                modifier_options: modifierOptions,
                quantity_info: {
                    quantity: {
                        min_permitted: bundle.required ? (bundle.min || 1) : 0,
                        max_permitted: bundle.max || 1
                    }
                }
            };
            modifierGroups.push(modifierGroup);
            // Create menu items for each modifier option
            for (const value of bundle.values) {
                // Use any type to handle the dynamic structure
                const bundleValue = value;
                const modifierItem = {
                    id: `${variant.id}-${bundle.key}-${bundleValue.sku || bundleValue.title || 'option'}`,
                    title: {
                        translations: {
                            [language]: bundleValue.title || ''
                        }
                    },
                    external_data: bundleValue.sku || '',
                    price_info: {
                        price: (bundleValue.price || 0) * 100 // UberEats requires price in cents
                    },
                    tax_info: {}
                };
                if (bundleValue.images && bundleValue.images.length > 0) {
                    modifierItem.image_url = bundleValue.images[0];
                }
                // Add the modifier item to the items array
                modifierItems.push(modifierItem);
            }
        }
        return { modifierGroups, modifierGroupIds, modifierItems };
    }
    fromProduct(products, variants, place, menuId, language) {
        const categories = this.categories(products, variants, language), menu = {
            menus: [{
                    id: menuId,
                    title: {
                        translations: {
                            [language]: menuId
                        }
                    },
                    service_availability: this.availability(place),
                    category_ids: categories.map(c => c.id),
                    invisible: false
                }],
            categories,
            items: [],
            modifier_groups: []
        };
        for (const variant of variants) {
            const product = products.find(p => String(p.id) === String(variant.productId));
            if (!product)
                continue;
            // Create modifier groups from bundleList
            if (product.bundleList && product.bundleList.length > 0) {
                const { modifierGroups, modifierGroupIds, modifierItems } = this.createModifierGroups(product, variant, language);
                // Add modifier groups to the menu
                menu.modifier_groups.push(...modifierGroups);
                // Add modifier items to the menu
                menu.items.push(...modifierItems);
                // Create the menu item with modifier group references
                const item = this.item(product, variant, language);
                // Add modifier group references to the item
                if (modifierGroupIds.length > 0) {
                    item.modifier_group_ids = {
                        ids: modifierGroupIds
                    };
                }
                menu.items.push(item);
            }
            else {
                // Add regular item without modifier groups
                menu.items.push(this.item(product, variant, language));
            }
        }
        return menu;
    }
    availability(place) {
        const { available, hours } = place.deliver ?? {}, { periods: openingPeriods = [] } = place.openingHours ?? {};
        let { periods = [] } = hours ?? {};
        if (available && periods.length === 0)
            periods = openingPeriods; // fallback to store opening hours
        const dayMap = {
            1: "monday",
            2: "tuesday",
            3: "wednesday",
            4: "thursday",
            5: "friday",
            6: "saturday",
            7: "sunday"
        }, format = {}, serviceAvailability = [];
        for (const period of periods) {
            const day = dayMap[period.open.day];
            format[day] = format[day] || [];
            format[day].push({
                start_time: period.open.time,
                end_time: period.close.time
            });
        }
        for (const day in format) {
            serviceAvailability.push({
                day_of_week: day,
                time_periods: format[day]
            });
        }
        return serviceAvailability;
    }
    /**
 * Organise given (CRM) variants into a Categories
 * ref: https://developer.grab.com/docs/grabfood/api/v1-1-3#section/Menu-structure-Item-Selling-Time
 *		 - minimum 1 category
 */
    categories(products, variants, language) {
        const format = {};
        for (const product of products) {
            const [category] = product.tags?.category || [];
            if (!category)
                throw new Error(`Product id: ${product.id} must have a category`);
            format[category] = format[category] || {
                id: category,
                title: {
                    translations: {
                        [language]: category
                    }
                },
                entities: []
            };
            const entities = variants.filter(v => String(v.productId) === String(product.id)).map(v => ({ id: String(v.id) }));
            format[category].entities.push(...entities);
        }
        return Object.values(format);
    }
    /**
     * Transform CRM product to UberEats Menu Item
     */
    item(product, variant, language) {
        const { title, description } = product, { id, sku, images, prices } = variant, { price: base = { value: 999 } } = prices.find(p => p.name === 'base') ?? {}, { value: price = 999 } = base, [image = { original: undefined }] = images, item = {
            id: String(id),
            external_data: sku,
            title: {
                translations: {
                    [language]: title
                }
            },
            description: {
                translations: {
                    [language]: (0, striptags_1.default)(description)
                }
            },
            image_url: image.original?.url,
            price_info: {
                price: price * 100, // UberEats requires price in cents, even for NTD or JPY
                overrides: []
            },
            quantity_info: {
                quantity: {},
                overrides: []
            },
            suspension_info: {
                overrides: []
            },
            tax_info: {},
            nutritional_info: {
                allergens: null
            },
            dish_info: {
                classifications: {
                    alcoholic_items: 0,
                    ingredients: null,
                    additives: null
                }
            },
            tax_label_info: {
                default_value: {
                    labels: [
                        'CAT_PREPARED_FOOD',
                        'TEMP_HEATED'
                    ],
                    source: 'MANUAL'
                }
            },
            product_info: {
                product_traits: [],
                countries_of_origin: []
            },
            bundled_items: null
        };
        return item;
    }
}
exports.Products = Products;
//# sourceMappingURL=products.js.map
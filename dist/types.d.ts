/**
 * Ref:  https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
 */
import { RemoteConfig } from '@perkd/api-request';
import { Products, Contacts, Places } from '@crm/types';
export type Address = Contacts.Address;
export type Phone = Contacts.Phone;
export type Product = Products.Product;
export type Variant = Products.Variant & {
    images: Products.Image[];
};
export type Place = Places.Place;
export declare enum Scope {
    STORE = "eats.store",
    ORDER = "eats.order"
}
export type Credentials = {
    clientId: string;
    secret: string;
    merchantId: string;
    scope: Scope;
    accessToken?: string;
    expiresAt?: Date;
};
export type Config = RemoteConfig & {};
export declare const SERVICE: {
    AUTH: string;
    STORE: string;
    MENU: string;
    ORDER: string;
};
/**
 * Comprehensive denial reasons for UberEats orders
 * Based on UberEats API documentation and best practices
 */
export declare enum DenialReason {
    /** Restaurant is too busy to fulfill orders */
    RESTAURANT_TOO_BUSY = "RESTAURANT_TOO_BUSY",
    /** Restaurant kitchen is closed */
    KITCHEN_CLOSED = "KITCHEN_CLOSED",
    /** Specific menu items are unavailable */
    ITEM_UNAVAILABLE = "ITEM_UNAVAILABLE",
    /** Items are out of stock */
    OUT_OF_STOCK = "OUT_OF_STOCK",
    /** Technical issues with POS integration */
    POS_INTEGRATION_ERROR = "POS_INTEGRATION_ERROR",
    /** System technical issues */
    SYSTEM_ERROR = "SYSTEM_ERROR",
    /** Preparation time exceeds delivery window */
    PREPARATION_TIME_TOO_LONG = "PREPARATION_TIME_TOO_LONG",
    /** Order received after restaurant cutoff time */
    PAST_CUTOFF_TIME = "PAST_CUTOFF_TIME",
    /** Restaurant at full capacity */
    FULL_CAPACITY = "FULL_CAPACITY",
    /** Invalid or problematic order */
    INVALID_ORDER = "INVALID_ORDER",
    /** Address delivery issues */
    DELIVERY_ADDRESS_ISSUE = "DELIVERY_ADDRESS_ISSUE",
    /** General inability to fulfill */
    UNABLE_TO_FULFILL = "UNABLE_TO_FULFILL"
}
/**
 * Cancel reasons aligned with denial reasons for consistency
 */
export declare enum CancelReason {
    RESTAURANT_TOO_BUSY = "RESTAURANT_TOO_BUSY",
    KITCHEN_CLOSED = "KITCHEN_CLOSED",
    ITEM_UNAVAILABLE = "ITEM_UNAVAILABLE",
    OUT_OF_STOCK = "OUT_OF_STOCK",
    SYSTEM_ERROR = "SYSTEM_ERROR",
    PREPARATION_TIME_TOO_LONG = "PREPARATION_TIME_TOO_LONG",
    FULL_CAPACITY = "FULL_CAPACITY",
    INVALID_ORDER = "INVALID_ORDER",
    DELIVERY_ADDRESS_ISSUE = "DELIVERY_ADDRESS_ISSUE",
    UNABLE_TO_FULFILL = "UNABLE_TO_FULFILL",
    /** Customer requested cancellation */
    CUSTOMER_CANCELLED = "CUSTOMER_CALLED_TO_CANCEL",
    /** Other unspecified reason */
    OTHER = "OTHER"
}
/**
 * Options for enhanced order rejection
 */
export interface RejectOptions {
    /** Reason for denial */
    reason?: DenialReason;
    /** Additional details/context */
    details?: string;
    /** Whether to force reject (deny + cancel) */
    forceReject?: boolean;
    /** Custom cancel code if force rejecting */
    cancelCode?: number;
}
/**
 * Mapping denial reasons to appropriate cancel codes
 */
export declare const DENIAL_TO_CANCEL_CODE: Record<DenialReason, number>;
/**
 * Mapping denial reasons to cancel reasons for consistency
 */
export declare const DENIAL_TO_CANCEL_REASON: Record<DenialReason, CancelReason>;
export declare enum EntityType {
    ITEM = "ITEM",
    MODIFIER_GROUP = "MODIFIER_GROUP"
}
export declare enum ContextType {
    MENU = "MENU",
    ITEM = "ITEM",
    MODIFIER_GROUP = "MODIFIER_GROUP"
}
export declare enum DisplayType {
    EXPANDED = "expanded",
    COLLAPSED = "collapsed"
}
export declare enum OrderState {
    CREATED = "CREATED",
    OFFERED = "OFFERED",
    ACCEPTED = "ACCEPTED",
    HANDED_OFF = "HANDED_OFF",
    SUCCEEDED = "SUCCEEDED",
    FAILED = "FAILED",
    UNKNOWN = "UNKNOWN"
}
export declare enum OrderStatus {
    SCHEDULED = "SCHEDULED",
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED"
}
export declare enum PrepStatus {
    PREPARING = "PREPARING",
    OUT_OF_ITEM = "OUT_OF_ITEM_PENDING_CUSTOMER_RESPONSE",
    READY = "READY_FOR_HANDOFF"
}
export declare enum DeliveryStatus {
    SCHEDULED = "SCHEDULED"
}
export declare enum FulfillmentType {
    UBER_DELIVER = "DELIVERY_BY_UBER",
    MERCHANT_DELIVER = "DELIVERY_BY_MERCHANT",
    PICK_UP = "PICKUP",
    DINE_IN = "DINE_IN"
}
export declare enum InteractionType {
    TO_DOOR = "DELIVER_TO_DOOR",
    CURBSIDE = "CURBSIDE"
}
export declare enum PriceType {
    ITEM = "ITEM"
}
export declare enum CancelType {
    KITCHEN_CLOSED = "KITCHEN_CLOSED",
    KITCHEN_BUSY = "RESTAURANT_TOO_BUSY",
    INVALID_ITEM = "ITEM_ISSUE",
    INVALID_ORDER = "ORDER_VALIDATION",
    INVALID_ADDRESS = "ADDRESS",
    FULL_CAPACITY = "CAPACITY",
    OTHER = "OTHER",
    CUSTOMER_REQUEST = "CUSTOMER_CALLED_TO_CANCEL"
}
export type MultiLanguageText = {
    translations: {
        [language: string]: string;
    };
};
export type Store = {
    id: string;
    name: string;
    timezone: string;
    partner_identifiers: {
        value: string;
        type: string;
    }[];
};
type Contact = {
    phone: {
        country_iso2: string;
        number: string;
        pin_code: string;
    };
};
type TaxProfile = {
    tax_id: string;
    tax_id_type: string;
    profile_type: string;
    country: string;
};
export type Customer = {
    id: string;
    name: {
        display_name: string;
        first_name: string;
        last_name: string;
    };
    contact: Contact;
    tax_profiles: TaxProfile[];
    order_history: {
        past_order_count: number;
    };
    is_primary_customer: boolean;
    can_respond_to_fulfillment_issues: boolean;
};
type Quantity = {
    amount: number;
    unit: string;
};
export type MenuEntity = {
    id?: string;
    type?: EntityType;
};
type QuantityConstraint = {
    min_permitted?: number;
    max_permitted?: number;
    is_min_permitted_optional?: boolean;
    default_quantity?: number;
    charge_above?: number;
    refund_under?: number;
    min_permitted_unique?: number;
    max_permitted_unique?: number;
};
type QuantityConstraintOverride = {
    context_type: ContextType;
    context_value: string;
    quantity: QuantityConstraint;
};
type QuantityConstraintRules = {
    quantity: QuantityConstraint;
    overrides?: QuantityConstraintOverride[];
};
export type ModifierGroup = {
    id: string;
    title: MultiLanguageText;
    external_data: string;
    modifier_options: MenuEntity[];
    quantity_info?: QuantityConstraintRules;
    display_type?: DisplayType;
};
declare enum MeasurementType {
    COUNT = "MEASUREMENT_TYPE_COUNT",
    WEIGHT = "MEASUREMENT_TYPE_WEIGHT",
    VOLUME = "MEASUREMENT_TYPE_VOLUME",
    LENGTH = "MEASUREMENT_TYPE_LENGTH",
    INVALID = "MEASUREMENT_TYPE_INVALID"
}
declare enum LengthUnit {
    METER = "LENGTH_UNIT_TYPE_METRIC_METER",
    CENTIMETER = "LENGTH_UNIT_TYPE_METRIC_CENTIMETER",
    MILLIMETER = "LENGTH_UNIT_TYPE_METRIC_MILLIMETER"
}
declare enum WeightUnit {
    KILOGRAM = "WEIGHT_UNIT_TYPE_METRIC_KILOGRAM",
    GRAM = "WEIGHT_UNIT_TYPE_METRIC_GRAM",
    MILLIGRAM = "WEIGHT_UNIT_TYPE_METRIC_MILLIGRAM",
    POUND = "WEIGHT_UNIT_TYPE_IMPERIAL_POUND",
    OUNCE = "WEIGHT_UNIT_TYPE_IMPERIAL_OUNCE"
}
declare enum VolumeUnit {
    LITER = "VOLUME_UNIT_TYPE_METRIC_LITER",
    MILLILITER = "VOLUME_UNIT_TYPE_METRIC_MILLILITER",
    OUNCE = "VOLUME_UNIT_TYPE_US_FLUID_OUNCE"
}
type MeasurementUnit = {
    measurement_type: MeasurementType;
    length_unit?: LengthUnit;
    weight_unit?: WeightUnit;
    volume_unit?: VolumeUnit;
};
type TaxInfo = {
    tax_rate?: number;
    vat_rate_percentage?: number;
};
type PriceOverride = {
    context_type: ContextType;
    context_value: string;
    price: number;
    core_price?: number;
};
type PriceRules = {
    price: number;
    core_price?: number;
    container_deposit?: number;
    overrides?: PriceOverride[];
    priced_by_unit?: MeasurementUnit;
};
type ModifierGroupsOverride = {
    context_type: ContextType;
    context_value: string;
    ids: string[];
};
type ModifierGroupsRules = {
    ids: string[];
    overrides?: ModifierGroupsOverride[];
};
type ProductInfo = {
    gtin?: string;
    plu?: string;
    merchant_id?: string;
    product_type?: string;
    product_traits?: string[];
    countries_of_origin?: string[];
    target_market?: number;
};
type SellingOption = {
    sold_by_unit?: MeasurementType;
};
type SellingInfo = {
    selling_options: SellingOption[];
};
export type CartItem = {
    id: string;
    cart_item_id: string;
    customer_id: string;
    title: string;
    external_data: string;
    quantity: Quantity;
    selected_modifier_groups?: ModifierGroup[];
    picture_url?: string;
    customer_request?: {
        special_instructions?: string;
    };
};
type Cart = {
    id: string;
    revision_id: string;
    items: CartItem[];
    include_single_use_items: boolean;
    special_instructions: string;
    restricted_items: {
        alcohol: {
            contain_alcoholic_item: boolean;
        };
    };
};
type Location = {
    id: string;
    type: string;
    street_address_line_one: string;
    street_address_line_two: string;
    unit_number: string;
    business_name?: string;
    latitude: string;
    longitude: string;
    city: string;
    country: string;
    postal_code: string;
    location_type_value: string;
    client_provided_street_address_line_one: string;
};
type Delivery = {
    id: string;
    status: DeliveryStatus;
    location?: Location;
    interaction_type: InteractionType;
    instructions: string;
    estimated_pick_up_time: Date;
    estimated_drop_off_time: Date;
};
export type Amount = {
    amount_e5: number;
    currency_code: string;
    formatted: string;
};
type Total = {
    gross: Amount;
    display_amount: string;
    net?: Amount;
    tax?: Amount;
    is_tax_inclusive?: boolean;
};
type Price = {
    cart_item_id: string;
    price_type: PriceType;
    quantity: Quantity;
    total: Total;
    unit: Total;
};
type Eligible = {
    is_eligible: boolean;
    reason: string;
};
export type Payment = {
    payment_detail: {
        order_total: Total;
        item_charges: {
            total: Total;
            price_breakdown: Price[];
        };
        fees: {
            total: Total;
        };
        promotions: {
            total: Total;
            order_total_excluding_promos: Total;
        };
        cash_amount_due?: {
            gross: Total['gross'];
        };
        currency_code: string;
        marketplace_fee_due_to_uber: Total;
    };
    tax_reporting: {
        breakdown: {
            items: any[];
            fees: any[];
            promotions: any[];
        };
    };
    tender_types: string | null;
};
export type Order = {
    id: string;
    display_id: string;
    state: OrderState;
    status: OrderStatus;
    store: Store;
    customers: Customer[];
    carts: Cart[];
    deliveries: Delivery[];
    payment: Payment;
    fulfillment_type: FulfillmentType;
    store_instructions?: string;
    created_time: Date;
    preparation_time: {
        source: string;
        ready_for_pickup_time_secs: number;
        ready_for_pickup_time: Date;
    };
    preparation_status: PrepStatus;
    action_eligibility: {
        adjust_ready_for_pickup_time: Eligible;
        mark_out_of_item: Eligible;
        cancel: Eligible;
        mark_cannot_fulfill: Eligible;
    };
    storeId?: string;
    external?: any;
};
export type AppOrderOptions = {
    createdAt?: Date;
    sendReceipt?: boolean;
};
export declare enum MenuType {
    DELIVER = "MENU_TYPE_FULFILLMENT_DELIVERY",
    PICKUP = "MENU_TYPE_FULFILLMENT_PICK_UP",
    DINEIN = "MENU_TYPE_FULFILLMENT_DINE_IN"
}
export declare enum DayOfWeek {
    MONDAY = "monday",
    TUESDAY = "tuesday",
    WEDNESDAY = "wednesday",
    THURSDAY = "thursday",
    FRIDAY = "friday",
    SATURDAY = "saturday",
    SUNDAY = "sunday"
}
export type TimePeriod = {
    start_time: string;
    end_time: string;
};
export type ServiceAvailability = {
    day_of_week: DayOfWeek;
    time_periods: TimePeriod[];
};
export type Menu = {
    id: string;
    title: MultiLanguageText;
    subtitle?: MultiLanguageText;
    service_availability: ServiceAvailability[];
    category_ids: string[];
    invisible: boolean;
};
export type Category = {
    id: string;
    title: MultiLanguageText;
    subtitle?: MultiLanguageText;
    entities: MenuEntity[];
};
export type MenuItem = {
    id: string;
    title: MultiLanguageText;
    tax_info: TaxInfo;
    description?: MultiLanguageText;
    image_url?: string;
    external_data?: string;
    price_info: PriceRules;
    quantity_info?: QuantityConstraintRules;
    modifier_group_ids?: ModifierGroupsRules;
    product_info?: ProductInfo;
    selling_info?: SellingInfo;
};
export type MenuConfiguration = {
    menus: Menu[];
    categories: Category[];
    items: MenuItem[];
    modifier_groups: ModifierGroup[];
};
export declare const EMPTY_MENU: any;
export type ListOptions = {
    day?: Date;
    next?: string;
};
export type OrderList = {
    orders: Order[];
    next?: string;
};
export {};

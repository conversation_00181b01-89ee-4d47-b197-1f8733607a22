"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UberSDK = void 0;
/**
 *  @module SDK class for Uber (Eats)
 */
const api_request_1 = require("@perkd/api-request");
const uber_remote_1 = require("./uber-remote");
const types_1 = require("./types");
const utils_1 = require("./utils");
const { CONTENT_ENCODING } = api_request_1.Headers, { STORE, ORDER, MENU } = types_1.SERVICE, SIGNATURE_HEADER = 'x-uber-signature', // converted to lowercase by AWS lambda, original: X-Uber-Signature
// merchant methods
STORE_STATUS = 'update-store-status', 
// store status
ONLINE = 'ONLINE', OFFLINE = 'OFFLINE', 
// order methods
ACCEPT = 'accept', DENY = 'deny', READY = 'ready', // ready for pickup
DELIVERY = 'delivery', CANCEL = 'cancel', TOO_BUSY = 'RESTAURANT_TOO_BUSY';
class UberSDK {
    api;
    constructor(credentials, options) {
        this.api = new uber_remote_1.UberRemote(credentials, options);
    }
    // --- Store ---
    /**
     * Temporarily pause store
     *	ref:	https://developer.uber.com/docs/eats/references/api/store_suite#tag/SetStoreStatus
     * @param storeId
     * @param isPause - false => resume
     * @param [duration] - '30m', '1h' or '24h' (when isPause = true)
     */
    async pauseStore(storeId, isPause = true, duration) {
        try {
            const { api } = this, url = api.url(STORE, STORE_STATUS, { storeId }), status = isPause ? OFFLINE : ONLINE, is_offline_until = isPause ? (0, utils_1.duration2Until)(duration) : undefined, reason = 'full';
            await api.post(url, { status, reason, is_offline_until });
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * Get Menu
     * 		https://developer.uber.com/docs/eats/references/api/v2/get-eats-stores-storeid-menu
     * @param storeId
     * @param type of menu
     */
    async getMenu(storeId, type = types_1.MenuType.DELIVER) {
        try {
            const { api } = this, url = api.url(MENU, '?menu_type={type}', { storeId, type });
            return api.get(url);
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * Upsert Menu
     * 		https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
     * @param storeId
     * @param menu
     */
    async updateMenu(storeId, menu) {
        try {
            const { api } = this, url = api.url(MENU, undefined, { storeId }), body = await (0, api_request_1.gzip)(menu), headers = { [CONTENT_ENCODING]: 'gzip' };
            await api.put(url, body, { headers });
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * Remove Menu
     * @param storeId
     * @param menuId
     */
    async removeMenu(storeId, menuId) {
        try {
            const { api } = this, url = api.url(MENU, undefined, { storeId });
            types_1.EMPTY_MENU.menus[0].id = menuId;
            await api.put(url, types_1.EMPTY_MENU);
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    // --- Order ---
    /**
     * Accept order
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/AcceptOrder/operation/acceptOrder
     * @param orderId
     * @param accept
     * @param readyAt
     */
    async acceptOrder(orderId, accept = true, readyAt, external_id) {
        try {
            const { api } = this, state = accept ? ACCEPT : DENY, ready_for_pickup_time = accept
                ? readyAt?.toISOString()
                : undefined, deny_reason = accept
                ? undefined
                : { type: TOO_BUSY, info: 'overload' }, url = api.url(ORDER, state, { orderId });
            await api.post(url, { ready_for_pickup_time, deny_reason, external_id });
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * Cancel order
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/CancelOrder
     * @param orderId
     * @param cancelCode
     */
    async cancelOrder(orderId, cancelCode = 1001) {
        try {
            const { api } = this, url = api.url(ORDER, CANCEL, { orderId }), type = TOO_BUSY, client_error_code = cancelCode;
            await api.post(url, { type, client_error_code });
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * Get active Order
     * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
     * @param orderId
     */
    async getOrder(orderId) {
        try {
            const { api } = this, url = api.url(ORDER, '?expand=carts,deliveries,payment', { orderId }), { order } = await api.get(url) ?? {};
            return order;
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    /**
     * List of orders for day
     * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
     * @param storeId
     * @param day - not supported for now
     * @param next_page_token
     */
    async listOrders(storeId, day = new Date(), next_page_token) {
        try {
            const { api } = this, url = api.url(STORE, 'orders', { storeId, next_page_token }), res = await api.get(url) ?? {};
            return res;
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    // --- Fulfillment ---
    /**
     * Mark order as Ready for delivery or completed for dine-in
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/OrderReady/operation/orderReady
     * @param orderId
     * @param markStatus 1 = ready (for delivery), 2 = completed (dine-in)
     */
    async orderReady(orderId, markStatus = 1) {
        try {
            const { api } = this, url = api.url(ORDER, READY, { orderId });
            await api.post(url);
        }
        catch (err) {
            throw this.handleError(err);
        }
    }
    verifyWebhook(headers, body) {
        const { api } = this, signature = headers[SIGNATURE_HEADER] || '', content = (typeof body === 'string') ? body : JSON.stringify(body), hmac = api.signature(content);
        return signature === hmac;
    }
    handleError(res) {
        const { context, stack } = res, { response, message } = context || res, { status, code } = response || res;
        return { code: code || status, message };
    }
}
exports.UberSDK = UberSDK;
//# sourceMappingURL=sdk.js.map
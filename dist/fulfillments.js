"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fulfillments = void 0;
const providers_1 = require("@provider/providers");
class Fulfillments extends providers_1.Base {
    constructor(provider) {
        super(provider);
    }
    async ready(orderId, status) {
        await this.API.orderReady(orderId, status);
    }
    // async collected(orderId: string) {
    // 	await this.API.orderCollected(orderId)
    // }
    // async delivered(orderId: string) {
    // 	await this.API.orderDelivered(orderId)
    // }
    async create(params, orderId) {
    }
    async complete(fulfillmentId, orderId) {
    }
    async cancel(fulfillmentId, orderId) {
    }
    async list(orderId) {
        return [];
    }
}
exports.Fulfillments = Fulfillments;
//# sourceMappingURL=fulfillments.js.map
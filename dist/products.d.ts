import { Products as Base, Provider } from '@provider/providers';
import { UberSDK } from './sdk';
import { MenuConfiguration, Place, Product, Variant } from './types';
export declare class Products extends Base {
    readonly API: UberSDK;
    readonly merchantId: string;
    constructor(provider: Provider, merchantId: string);
    get(shopId?: string): Promise<MenuConfiguration>;
    update(menu: MenuConfiguration, shopId?: string): Promise<void>;
    refresh(menu: MenuConfiguration): Promise<void>;
    /**
     * Transform bundleList from product to UberEats ModifierGroups
     */
    private createModifierGroups;
    fromProduct(products: Product[], variants: Variant[], place: Place, menuId: string, language: string): MenuConfiguration;
    private availability;
    /**
 * Organise given (CRM) variants into a Categories
 * ref: https://developer.grab.com/docs/grabfood/api/v1-1-3#section/Menu-structure-Item-Selling-Time
 *		 - minimum 1 category
 */
    private categories;
    /**
     * Transform CRM product to UberEats Menu Item
     */
    private item;
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UberRemote = void 0;
const node_crypto_1 = require("node:crypto");
const api_request_1 = require("@perkd/api-request");
const types_1 = require("./types");
const ENDPOINTS = require('./Uber-endpoints.json'), { AUTHORIZATION, CONTENT_TYPE } = api_request_1.Headers, { FORM_URLENCODED, JSON_DATA } = api_request_1.MediaType, { CLIENT_CREDENTIALS } = api_request_1.OAuth.GrantType, { AUTH } = types_1.SERVICE, TEST = 'test';
class UberRemote extends api_request_1.ApiRemote {
    credentials;
    constructor(credentials, settings = {}) {
        const { clientId, secret, scope, merchantId } = credentials, { axios = {}, ...rest } = settings;
        if (!clientId || !secret || !scope || !merchantId)
            throw 'Incomplete credentials';
        axios.withCredentials = true;
        axios.headers = { [CONTENT_TYPE]: JSON_DATA };
        super({ ...rest, axios });
        this.credentials = credentials;
        this.remote.client.interceptors.request.use((config) => {
            const { accessToken } = this;
            if (accessToken) {
                config.headers[AUTHORIZATION] = `Bearer ${accessToken}`;
            }
            return config;
        });
        this.remote.on(api_request_1.EVENT.unauthorized, ({ retryCount, lastRequestTime, url }) => {
            if (url !== this.url(AUTH)) {
                this.refreshToken();
            }
        });
    }
    get accessToken() {
        return this.credentials.accessToken;
    }
    get secret() {
        return this.credentials.secret;
    }
    get clientId() {
        return this.credentials.clientId;
    }
    get scope() {
        return this.credentials.scope;
    }
    async refreshToken() {
        try {
            const { clientId: client_id, secret: client_secret, scope } = this, url = this.url(AUTH), headers = { [CONTENT_TYPE]: FORM_URLENCODED }, grant_type = CLIENT_CREDENTIALS, body = { client_id, client_secret, grant_type, scope }, credentials = await this.post(url, body, { headers }), { access_token, expires_in } = credentials ?? {};
            if (access_token) {
                this.credentials.accessToken = access_token;
                this.credentials.expiresAt = new Date(Date.now() + expires_in * 1000);
            }
        }
        catch (err) {
            const { context = {} } = err, { message, data } = context;
            console.error(message, data);
        }
    }
    signature(content) {
        const { secret } = this, signature = (0, node_crypto_1.createHmac)('sha256', secret)
            .update(content)
            .digest('hex');
        return signature;
    }
    endpoint(service) {
        const { env = TEST, scope } = this, [foodOrMart] = scope.split('.');
        return ENDPOINTS[foodOrMart][env][service];
    }
}
exports.UberRemote = UberRemote;
//# sourceMappingURL=uber-remote.js.map
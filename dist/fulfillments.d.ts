import { Base, Provider, FulfillmentsInterface } from '@provider/providers';
import { UberSDK } from './sdk';
type Id = string;
type Fulfillment = any;
export declare class Fulfillments extends Base implements FulfillmentsInterface {
    readonly API: UberSDK;
    constructor(provider: Provider);
    ready(orderId: string, status?: number): Promise<void>;
    create(params: any, orderId?: Id): Promise<Fulfillment>;
    complete(fulfillmentId: Id, orderId?: Id): Promise<void>;
    cancel(fulfillmentId: Id, orderId: Id): Promise<void>;
    list(orderId: Id): Promise<Fulfillment[]>;
}
export {};

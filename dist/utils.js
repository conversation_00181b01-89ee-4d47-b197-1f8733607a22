"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.amount2Native = amount2Native;
exports.taxRate = taxRate;
exports.cleanse = cleanse;
exports.duration2Until = duration2Until;
const utils_1 = require("@perkd/utils");
const { nativeAmount } = utils_1.Currencies, ONE_DAY = 24 * 60 * 60 * 1000, // milliseconds
ONE_YEAR = ONE_DAY * 365, NOW = new Date().getTime(), defaultStart = new Date(NOW - ONE_YEAR), defaultEnd = new Date(NOW + ONE_YEAR);
defaultStart.setUTCHours(16, 0, 0, 0);
defaultEnd.setUTCHours(15, 59, 59, 0);
function amount2Native(amount) {
    if (!amount)
        return 0;
    const { amount_e5, currency_code } = amount, price = amount_e5 / 100000;
    return nativeAmount(price, currency_code);
}
function taxRate(tax_reporting = {}) {
    const { breakdown } = tax_reporting, [item] = breakdown?.items ?? {}, [tax] = item?.taxes ?? {};
    return tax.rate ?? 0;
}
function cleanse(text) {
    if (!text)
        return '';
    const regexPatterns = [/\| GUI\/TIN: \d+$/];
    for (const pattern of regexPatterns) {
        text = text.replace(pattern, "");
    }
    return text.trim();
}
// ----  Private functions
/**
 * Convert duration to timestamp RFC3339 format
 * @param duration - '30m', '1h' or '24h'
 */
function duration2Until(duration = '1h') {
    const minutes = duration === '30m'
        ? 30
        : duration === '1h' ? 60 : 24 * 60;
    return new Date(Date.now() + minutes * 60000).toISOString();
}
//# sourceMappingURL=utils.js.map
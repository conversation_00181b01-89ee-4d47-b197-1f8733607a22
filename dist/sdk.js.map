{"version": 3, "file": "sdk.js", "sourceRoot": "", "sources": ["../src/sdk.ts"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,oDAAkD;AAClD,+CAA0C;AAC1C,mCAA2K;AAC3K,mCAAwC;AAExC,MAAM,EAAE,gBAAgB,EAAE,GAAG,qBAAO,EACnC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,eAAO,EAChC,gBAAgB,GAAG,kBAAkB,EAAE,mEAAmE;AAC1G,mBAAmB;AACnB,YAAY,GAAG,qBAAqB;AACpC,eAAe;AACf,MAAM,GAAG,QAAQ,EACjB,OAAO,GAAG,SAAS;AACnB,gBAAgB;AAChB,MAAM,GAAG,QAAQ,EACjB,IAAI,GAAG,MAAM,EACb,KAAK,GAAG,OAAO,EAAE,mBAAmB;AACpC,QAAQ,GAAG,UAAU,EACrB,MAAM,GAAG,QAAQ,CAAA;AAElB,MAAa,OAAO;IAEV,GAAG,CAAY;IAExB,YAAY,WAAwB,EAAE,OAAgB;QACrD,IAAI,CAAC,GAAG,GAAG,IAAI,wBAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IAChD,CAAC;IAED,gBAAgB;IAEhB;;;;;;;OAOG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,UAAmB,IAAI,EAAE,QAAiB;QAC3E,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,EAAE,EAAE,OAAO,EAAE,CAAC,EAC/C,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EACnC,gBAAgB,GAAG,OAAO,CAAC,CAAC,CAAC,IAAA,sBAAc,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,EACjE,MAAM,GAAG,MAAM,CAAA;YAEhB,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAA;QAC1D,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,OAAO,CAAC,OAAe,EAAE,OAAiB,gBAAQ,CAAC,OAAO;QAC/D,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;YAE5D,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACpB,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,IAAuB;QACxD,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,EAC3C,IAAI,GAAG,MAAM,IAAA,kBAAI,EAAC,IAAI,CAAC,EACvB,OAAO,GAAG,EAAE,CAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAA;YAEzC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;QACtC,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,MAAc;QAC/C,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;YAE5C,kBAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAA;YAC/B,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAU,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED,gBAAgB;IAEhB;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CAChB,OAAe,EACf,SAAkB,IAAI,EACtB,OAAc,EACd,WAAoB,EACpB,YAA2B,EAC3B,aAAsB;QAEtB,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAC9B,qBAAqB,GAAG,MAAM;gBAC7B,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;gBACxB,CAAC,CAAC,SAAS,EACZ,WAAW,GAAG,MAAM;gBACnB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC;oBACD,IAAI,EAAE,YAAY,IAAI,oBAAY,CAAC,mBAAmB;oBACtD,IAAI,EAAE,aAAa,IAAI,yBAAyB;iBAChD,EACF,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;YAEzC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,qBAAqB,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAA;QACzE,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,WAAW,CAChB,OAAe,EACf,eAA6B,oBAAY,CAAC,mBAAmB,EAC7D,UAAmB;QAEnB,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,CAAC,EACzC,IAAI,GAAG,YAAY,EACnB,iBAAiB,GAAG,UAAU,IAAI,IAAI,CAAA;YAEvC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAA;QACjD,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,OAAe;QAC7B,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,kCAAkC,EAAE,EAAE,OAAO,EAAE,CAAC,EACrE,EAAE,KAAK,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;YAErC,OAAO,KAAK,CAAA;QACb,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,MAAY,IAAI,IAAI,EAAE,EAAE,eAAwB;QACjF,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,EAC5D,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAA;YAE/B,OAAO,GAAG,CAAA;QACX,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED,sBAAsB;IAEtB;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CAAC,OAAe,EAAE,aAAqB,CAAC;QACvD,IAAI,CAAC;YACJ,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;YAEzC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACpB,CAAC;QACD,OAAO,GAAG,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;QAC5B,CAAC;IACF,CAAC;IAED,aAAa,CAAC,OAAY,EAAE,IAAkB;QAC7C,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAC3C,OAAO,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAClE,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QAE9B,OAAO,SAAS,KAAK,IAAI,CAAA;IAC1B,CAAC;IAEO,WAAW,CAAC,GAAQ;QAC3B,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,GAAG,EAC7B,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,IAAI,GAAG,EACtC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,IAAI,GAAG,CAAA;QAEnC,OAAO,EAAE,IAAI,EAAE,IAAI,IAAI,MAAM,EAAE,OAAO,EAAE,CAAA;IACzC,CAAC;CACD;AApOD,0BAoOC"}
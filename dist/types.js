"use strict";
/**
 * Ref:  https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EMPTY_MENU = exports.DayOfWeek = exports.MenuType = exports.CancelType = exports.PriceType = exports.InteractionType = exports.FulfillmentType = exports.DeliveryStatus = exports.PrepStatus = exports.OrderStatus = exports.OrderState = exports.DisplayType = exports.ContextType = exports.EntityType = exports.DENIAL_TO_CANCEL_REASON = exports.DENIAL_TO_CANCEL_CODE = exports.CancelReason = exports.DenialReason = exports.SERVICE = exports.Scope = void 0;
// ---  Uber
var Scope;
(function (Scope) {
    Scope["STORE"] = "eats.store";
    Scope["ORDER"] = "eats.order";
})(Scope || (exports.Scope = Scope = {}));
// Service endpoints
exports.SERVICE = {
    AUTH: 'auth',
    STORE: 'store',
    MENU: 'menu',
    ORDER: 'order',
};
/**
 * Comprehensive denial reasons for UberEats orders
 * Based on UberEats API documentation and best practices
 */
var DenialReason;
(function (DenialReason) {
    /** Restaurant is too busy to fulfill orders */
    DenialReason["RESTAURANT_TOO_BUSY"] = "RESTAURANT_TOO_BUSY";
    /** Restaurant kitchen is closed */
    DenialReason["KITCHEN_CLOSED"] = "KITCHEN_CLOSED";
    /** Specific menu items are unavailable */
    DenialReason["ITEM_UNAVAILABLE"] = "ITEM_UNAVAILABLE";
    /** Items are out of stock */
    DenialReason["OUT_OF_STOCK"] = "OUT_OF_STOCK";
    /** Technical issues with POS integration */
    DenialReason["POS_INTEGRATION_ERROR"] = "POS_INTEGRATION_ERROR";
    /** System technical issues */
    DenialReason["SYSTEM_ERROR"] = "SYSTEM_ERROR";
    /** Preparation time exceeds delivery window */
    DenialReason["PREPARATION_TIME_TOO_LONG"] = "PREPARATION_TIME_TOO_LONG";
    /** Order received after restaurant cutoff time */
    DenialReason["PAST_CUTOFF_TIME"] = "PAST_CUTOFF_TIME";
    /** Restaurant at full capacity */
    DenialReason["FULL_CAPACITY"] = "FULL_CAPACITY";
    /** Invalid or problematic order */
    DenialReason["INVALID_ORDER"] = "INVALID_ORDER";
    /** Address delivery issues */
    DenialReason["DELIVERY_ADDRESS_ISSUE"] = "DELIVERY_ADDRESS_ISSUE";
    /** General inability to fulfill */
    DenialReason["UNABLE_TO_FULFILL"] = "UNABLE_TO_FULFILL";
})(DenialReason || (exports.DenialReason = DenialReason = {}));
/**
 * Cancel reasons aligned with denial reasons for consistency
 */
var CancelReason;
(function (CancelReason) {
    CancelReason["RESTAURANT_TOO_BUSY"] = "RESTAURANT_TOO_BUSY";
    CancelReason["KITCHEN_CLOSED"] = "KITCHEN_CLOSED";
    CancelReason["ITEM_UNAVAILABLE"] = "ITEM_UNAVAILABLE";
    CancelReason["OUT_OF_STOCK"] = "OUT_OF_STOCK";
    CancelReason["SYSTEM_ERROR"] = "SYSTEM_ERROR";
    CancelReason["PREPARATION_TIME_TOO_LONG"] = "PREPARATION_TIME_TOO_LONG";
    CancelReason["FULL_CAPACITY"] = "FULL_CAPACITY";
    CancelReason["INVALID_ORDER"] = "INVALID_ORDER";
    CancelReason["DELIVERY_ADDRESS_ISSUE"] = "DELIVERY_ADDRESS_ISSUE";
    CancelReason["UNABLE_TO_FULFILL"] = "UNABLE_TO_FULFILL";
    /** Customer requested cancellation */
    CancelReason["CUSTOMER_CANCELLED"] = "CUSTOMER_CALLED_TO_CANCEL";
    /** Other unspecified reason */
    CancelReason["OTHER"] = "OTHER";
})(CancelReason || (exports.CancelReason = CancelReason = {}));
/**
 * Mapping denial reasons to appropriate cancel codes
 */
exports.DENIAL_TO_CANCEL_CODE = {
    [DenialReason.RESTAURANT_TOO_BUSY]: 1001,
    [DenialReason.KITCHEN_CLOSED]: 1002,
    [DenialReason.ITEM_UNAVAILABLE]: 1003,
    [DenialReason.OUT_OF_STOCK]: 1004,
    [DenialReason.POS_INTEGRATION_ERROR]: 1005,
    [DenialReason.SYSTEM_ERROR]: 1006,
    [DenialReason.PREPARATION_TIME_TOO_LONG]: 1007,
    [DenialReason.PAST_CUTOFF_TIME]: 1008,
    [DenialReason.FULL_CAPACITY]: 1009,
    [DenialReason.INVALID_ORDER]: 1010,
    [DenialReason.DELIVERY_ADDRESS_ISSUE]: 1011,
    [DenialReason.UNABLE_TO_FULFILL]: 1012
};
/**
 * Mapping denial reasons to cancel reasons for consistency
 */
exports.DENIAL_TO_CANCEL_REASON = {
    [DenialReason.RESTAURANT_TOO_BUSY]: CancelReason.RESTAURANT_TOO_BUSY,
    [DenialReason.KITCHEN_CLOSED]: CancelReason.KITCHEN_CLOSED,
    [DenialReason.ITEM_UNAVAILABLE]: CancelReason.ITEM_UNAVAILABLE,
    [DenialReason.OUT_OF_STOCK]: CancelReason.OUT_OF_STOCK,
    [DenialReason.POS_INTEGRATION_ERROR]: CancelReason.SYSTEM_ERROR,
    [DenialReason.SYSTEM_ERROR]: CancelReason.SYSTEM_ERROR,
    [DenialReason.PREPARATION_TIME_TOO_LONG]: CancelReason.PREPARATION_TIME_TOO_LONG,
    [DenialReason.PAST_CUTOFF_TIME]: CancelReason.RESTAURANT_TOO_BUSY,
    [DenialReason.FULL_CAPACITY]: CancelReason.FULL_CAPACITY,
    [DenialReason.INVALID_ORDER]: CancelReason.INVALID_ORDER,
    [DenialReason.DELIVERY_ADDRESS_ISSUE]: CancelReason.DELIVERY_ADDRESS_ISSUE,
    [DenialReason.UNABLE_TO_FULFILL]: CancelReason.UNABLE_TO_FULFILL
};
var EntityType;
(function (EntityType) {
    EntityType["ITEM"] = "ITEM";
    EntityType["MODIFIER_GROUP"] = "MODIFIER_GROUP";
})(EntityType || (exports.EntityType = EntityType = {}));
var ContextType;
(function (ContextType) {
    ContextType["MENU"] = "MENU";
    ContextType["ITEM"] = "ITEM";
    ContextType["MODIFIER_GROUP"] = "MODIFIER_GROUP";
})(ContextType || (exports.ContextType = ContextType = {}));
var DisplayType;
(function (DisplayType) {
    DisplayType["EXPANDED"] = "expanded";
    DisplayType["COLLAPSED"] = "collapsed";
})(DisplayType || (exports.DisplayType = DisplayType = {}));
var OrderState;
(function (OrderState) {
    OrderState["CREATED"] = "CREATED";
    OrderState["OFFERED"] = "OFFERED";
    OrderState["ACCEPTED"] = "ACCEPTED";
    OrderState["HANDED_OFF"] = "HANDED_OFF";
    OrderState["SUCCEEDED"] = "SUCCEEDED";
    OrderState["FAILED"] = "FAILED";
    OrderState["UNKNOWN"] = "UNKNOWN";
})(OrderState || (exports.OrderState = OrderState = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["SCHEDULED"] = "SCHEDULED";
    OrderStatus["ACTIVE"] = "ACTIVE";
    OrderStatus["COMPLETED"] = "COMPLETED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var PrepStatus;
(function (PrepStatus) {
    PrepStatus["PREPARING"] = "PREPARING";
    PrepStatus["OUT_OF_ITEM"] = "OUT_OF_ITEM_PENDING_CUSTOMER_RESPONSE";
    PrepStatus["READY"] = "READY_FOR_HANDOFF";
})(PrepStatus || (exports.PrepStatus = PrepStatus = {}));
var DeliveryStatus;
(function (DeliveryStatus) {
    DeliveryStatus["SCHEDULED"] = "SCHEDULED";
})(DeliveryStatus || (exports.DeliveryStatus = DeliveryStatus = {}));
var FulfillmentType;
(function (FulfillmentType) {
    FulfillmentType["UBER_DELIVER"] = "DELIVERY_BY_UBER";
    FulfillmentType["MERCHANT_DELIVER"] = "DELIVERY_BY_MERCHANT";
    FulfillmentType["PICK_UP"] = "PICKUP";
    FulfillmentType["DINE_IN"] = "DINE_IN";
})(FulfillmentType || (exports.FulfillmentType = FulfillmentType = {}));
var InteractionType;
(function (InteractionType) {
    InteractionType["TO_DOOR"] = "DELIVER_TO_DOOR";
    InteractionType["CURBSIDE"] = "CURBSIDE";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
var PriceType;
(function (PriceType) {
    PriceType["ITEM"] = "ITEM";
})(PriceType || (exports.PriceType = PriceType = {}));
var CancelType;
(function (CancelType) {
    CancelType["KITCHEN_CLOSED"] = "KITCHEN_CLOSED";
    CancelType["KITCHEN_BUSY"] = "RESTAURANT_TOO_BUSY";
    CancelType["INVALID_ITEM"] = "ITEM_ISSUE";
    CancelType["INVALID_ORDER"] = "ORDER_VALIDATION";
    CancelType["INVALID_ADDRESS"] = "ADDRESS";
    CancelType["FULL_CAPACITY"] = "CAPACITY";
    CancelType["OTHER"] = "OTHER";
    CancelType["CUSTOMER_REQUEST"] = "CUSTOMER_CALLED_TO_CANCEL";
})(CancelType || (exports.CancelType = CancelType = {}));
// ---  Order
var MeasurementType;
(function (MeasurementType) {
    MeasurementType["COUNT"] = "MEASUREMENT_TYPE_COUNT";
    MeasurementType["WEIGHT"] = "MEASUREMENT_TYPE_WEIGHT";
    MeasurementType["VOLUME"] = "MEASUREMENT_TYPE_VOLUME";
    MeasurementType["LENGTH"] = "MEASUREMENT_TYPE_LENGTH";
    MeasurementType["INVALID"] = "MEASUREMENT_TYPE_INVALID";
})(MeasurementType || (MeasurementType = {}));
var LengthUnit;
(function (LengthUnit) {
    LengthUnit["METER"] = "LENGTH_UNIT_TYPE_METRIC_METER";
    LengthUnit["CENTIMETER"] = "LENGTH_UNIT_TYPE_METRIC_CENTIMETER";
    LengthUnit["MILLIMETER"] = "LENGTH_UNIT_TYPE_METRIC_MILLIMETER";
})(LengthUnit || (LengthUnit = {}));
var WeightUnit;
(function (WeightUnit) {
    WeightUnit["KILOGRAM"] = "WEIGHT_UNIT_TYPE_METRIC_KILOGRAM";
    WeightUnit["GRAM"] = "WEIGHT_UNIT_TYPE_METRIC_GRAM";
    WeightUnit["MILLIGRAM"] = "WEIGHT_UNIT_TYPE_METRIC_MILLIGRAM";
    WeightUnit["POUND"] = "WEIGHT_UNIT_TYPE_IMPERIAL_POUND";
    WeightUnit["OUNCE"] = "WEIGHT_UNIT_TYPE_IMPERIAL_OUNCE";
})(WeightUnit || (WeightUnit = {}));
var VolumeUnit;
(function (VolumeUnit) {
    VolumeUnit["LITER"] = "VOLUME_UNIT_TYPE_METRIC_LITER";
    VolumeUnit["MILLILITER"] = "VOLUME_UNIT_TYPE_METRIC_MILLILITER";
    VolumeUnit["OUNCE"] = "VOLUME_UNIT_TYPE_US_FLUID_OUNCE";
})(VolumeUnit || (VolumeUnit = {}));
// ---  Menu
//	https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu#request-body-parameters-menuconfiguration
var MenuType;
(function (MenuType) {
    MenuType["DELIVER"] = "MENU_TYPE_FULFILLMENT_DELIVERY";
    MenuType["PICKUP"] = "MENU_TYPE_FULFILLMENT_PICK_UP";
    MenuType["DINEIN"] = "MENU_TYPE_FULFILLMENT_DINE_IN";
})(MenuType || (exports.MenuType = MenuType = {}));
var DayOfWeek;
(function (DayOfWeek) {
    DayOfWeek["MONDAY"] = "monday";
    DayOfWeek["TUESDAY"] = "tuesday";
    DayOfWeek["WEDNESDAY"] = "wednesday";
    DayOfWeek["THURSDAY"] = "thursday";
    DayOfWeek["FRIDAY"] = "friday";
    DayOfWeek["SATURDAY"] = "saturday";
    DayOfWeek["SUNDAY"] = "sunday";
})(DayOfWeek || (exports.DayOfWeek = DayOfWeek = {}));
exports.EMPTY_MENU = {
    menus: [{
            id: 'replace',
            title: {
                translations: { en_us: 'Empty Menu' }
            },
            service_availability: [{
                    day_of_week: 'monday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'tuesday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'wednesday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'thursday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'friday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'saturday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'sunday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }],
            category_ids: []
        }],
    items: [],
    categories: [],
    modifier_groups: [],
    display_options: {}
};
//# sourceMappingURL=types.js.map
"use strict";
/**
 * Ref:  https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EMPTY_MENU = exports.DayOfWeek = exports.MenuType = exports.CancelType = exports.PriceType = exports.InteractionType = exports.FulfillmentType = exports.DeliveryStatus = exports.PrepStatus = exports.OrderStatus = exports.OrderState = exports.DisplayType = exports.ContextType = exports.EntityType = exports.SERVICE = exports.Scope = void 0;
// ---  Uber
var Scope;
(function (Scope) {
    Scope["STORE"] = "eats.store";
    Scope["ORDER"] = "eats.order";
})(Scope || (exports.Scope = Scope = {}));
// Service endpoints
exports.SERVICE = {
    AUTH: 'auth',
    STORE: 'store',
    MENU: 'menu',
    ORDER: 'order',
};
var EntityType;
(function (EntityType) {
    EntityType["ITEM"] = "ITEM";
    EntityType["MODIFIER_GROUP"] = "MODIFIER_GROUP";
})(EntityType || (exports.EntityType = EntityType = {}));
var ContextType;
(function (ContextType) {
    ContextType["MENU"] = "MENU";
    ContextType["ITEM"] = "ITEM";
    ContextType["MODIFIER_GROUP"] = "MODIFIER_GROUP";
})(ContextType || (exports.ContextType = ContextType = {}));
var DisplayType;
(function (DisplayType) {
    DisplayType["EXPANDED"] = "expanded";
    DisplayType["COLLAPSED"] = "collapsed";
})(DisplayType || (exports.DisplayType = DisplayType = {}));
var OrderState;
(function (OrderState) {
    OrderState["CREATED"] = "CREATED";
    OrderState["OFFERED"] = "OFFERED";
    OrderState["ACCEPTED"] = "ACCEPTED";
    OrderState["HANDED_OFF"] = "HANDED_OFF";
    OrderState["SUCCEEDED"] = "SUCCEEDED";
    OrderState["FAILED"] = "FAILED";
    OrderState["UNKNOWN"] = "UNKNOWN";
})(OrderState || (exports.OrderState = OrderState = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["SCHEDULED"] = "SCHEDULED";
    OrderStatus["ACTIVE"] = "ACTIVE";
    OrderStatus["COMPLETED"] = "COMPLETED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
var PrepStatus;
(function (PrepStatus) {
    PrepStatus["PREPARING"] = "PREPARING";
    PrepStatus["OUT_OF_ITEM"] = "OUT_OF_ITEM_PENDING_CUSTOMER_RESPONSE";
    PrepStatus["READY"] = "READY_FOR_HANDOFF";
})(PrepStatus || (exports.PrepStatus = PrepStatus = {}));
var DeliveryStatus;
(function (DeliveryStatus) {
    DeliveryStatus["SCHEDULED"] = "SCHEDULED";
})(DeliveryStatus || (exports.DeliveryStatus = DeliveryStatus = {}));
var FulfillmentType;
(function (FulfillmentType) {
    FulfillmentType["UBER_DELIVER"] = "DELIVERY_BY_UBER";
    FulfillmentType["MERCHANT_DELIVER"] = "DELIVERY_BY_MERCHANT";
    FulfillmentType["PICK_UP"] = "PICKUP";
    FulfillmentType["DINE_IN"] = "DINE_IN";
})(FulfillmentType || (exports.FulfillmentType = FulfillmentType = {}));
var InteractionType;
(function (InteractionType) {
    InteractionType["TO_DOOR"] = "DELIVER_TO_DOOR";
    InteractionType["CURBSIDE"] = "CURBSIDE";
})(InteractionType || (exports.InteractionType = InteractionType = {}));
var PriceType;
(function (PriceType) {
    PriceType["ITEM"] = "ITEM";
})(PriceType || (exports.PriceType = PriceType = {}));
var CancelType;
(function (CancelType) {
    CancelType["KITCHEN_CLOSED"] = "KITCHEN_CLOSED";
    CancelType["KITCHEN_BUSY"] = "RESTAURANT_TOO_BUSY";
    CancelType["INVALID_ITEM"] = "ITEM_ISSUE";
    CancelType["INVALID_ORDER"] = "ORDER_VALIDATION";
    CancelType["INVALID_ADDRESS"] = "ADDRESS";
    CancelType["FULL_CAPACITY"] = "CAPACITY";
    CancelType["OTHER"] = "OTHER";
    CancelType["CUSTOMER_REQUEST"] = "CUSTOMER_CALLED_TO_CANCEL";
})(CancelType || (exports.CancelType = CancelType = {}));
// ---  Order
var MeasurementType;
(function (MeasurementType) {
    MeasurementType["COUNT"] = "MEASUREMENT_TYPE_COUNT";
    MeasurementType["WEIGHT"] = "MEASUREMENT_TYPE_WEIGHT";
    MeasurementType["VOLUME"] = "MEASUREMENT_TYPE_VOLUME";
    MeasurementType["LENGTH"] = "MEASUREMENT_TYPE_LENGTH";
    MeasurementType["INVALID"] = "MEASUREMENT_TYPE_INVALID";
})(MeasurementType || (MeasurementType = {}));
var LengthUnit;
(function (LengthUnit) {
    LengthUnit["METER"] = "LENGTH_UNIT_TYPE_METRIC_METER";
    LengthUnit["CENTIMETER"] = "LENGTH_UNIT_TYPE_METRIC_CENTIMETER";
    LengthUnit["MILLIMETER"] = "LENGTH_UNIT_TYPE_METRIC_MILLIMETER";
})(LengthUnit || (LengthUnit = {}));
var WeightUnit;
(function (WeightUnit) {
    WeightUnit["KILOGRAM"] = "WEIGHT_UNIT_TYPE_METRIC_KILOGRAM";
    WeightUnit["GRAM"] = "WEIGHT_UNIT_TYPE_METRIC_GRAM";
    WeightUnit["MILLIGRAM"] = "WEIGHT_UNIT_TYPE_METRIC_MILLIGRAM";
    WeightUnit["POUND"] = "WEIGHT_UNIT_TYPE_IMPERIAL_POUND";
    WeightUnit["OUNCE"] = "WEIGHT_UNIT_TYPE_IMPERIAL_OUNCE";
})(WeightUnit || (WeightUnit = {}));
var VolumeUnit;
(function (VolumeUnit) {
    VolumeUnit["LITER"] = "VOLUME_UNIT_TYPE_METRIC_LITER";
    VolumeUnit["MILLILITER"] = "VOLUME_UNIT_TYPE_METRIC_MILLILITER";
    VolumeUnit["OUNCE"] = "VOLUME_UNIT_TYPE_US_FLUID_OUNCE";
})(VolumeUnit || (VolumeUnit = {}));
// ---  Menu
//	https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu#request-body-parameters-menuconfiguration
var MenuType;
(function (MenuType) {
    MenuType["DELIVER"] = "MENU_TYPE_FULFILLMENT_DELIVERY";
    MenuType["PICKUP"] = "MENU_TYPE_FULFILLMENT_PICK_UP";
    MenuType["DINEIN"] = "MENU_TYPE_FULFILLMENT_DINE_IN";
})(MenuType || (exports.MenuType = MenuType = {}));
var DayOfWeek;
(function (DayOfWeek) {
    DayOfWeek["MONDAY"] = "monday";
    DayOfWeek["TUESDAY"] = "tuesday";
    DayOfWeek["WEDNESDAY"] = "wednesday";
    DayOfWeek["THURSDAY"] = "thursday";
    DayOfWeek["FRIDAY"] = "friday";
    DayOfWeek["SATURDAY"] = "saturday";
    DayOfWeek["SUNDAY"] = "sunday";
})(DayOfWeek || (exports.DayOfWeek = DayOfWeek = {}));
exports.EMPTY_MENU = {
    menus: [{
            id: 'replace',
            title: {
                translations: { en_us: 'Empty Menu' }
            },
            service_availability: [{
                    day_of_week: 'monday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'tuesday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'wednesday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'thursday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'friday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'saturday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }, {
                    day_of_week: 'sunday',
                    time_periods: [{ start_time: '00:00', end_time: '23:59' }]
                }],
            category_ids: []
        }],
    items: [],
    categories: [],
    modifier_groups: [],
    display_options: {}
};
//# sourceMappingURL=types.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Orders = void 0;
/* eslint-disable camelcase */
/**
 * Reference: https://developer.grab.com/docs/grabmart/api/v1-1-3/#tag/submit-order/operation/submit-order-api
 */
const types_1 = require("@crm/types");
const providers_1 = require("@provider/providers");
const utils_1 = require("@perkd/utils");
const types_2 = require("./types");
const utils_2 = require("./utils");
const { KITCHEN } = providers_1.CRM.FulfillmentService, { PRODUCT } = providers_1.CRM.ItemKind, { DELIVER, STORE, PICKUP } = providers_1.CRM.FulfillmentType, { UBER_DELIVER, PICK_UP, DINE_IN, MERCHANT_DELIVER } = types_2.FulfillmentType, { POINT } = providers_1.CRM.GeoType, { PENDING, OPEN } = types_1.Fulfillments.Status, { parsePhoneNumber } = utils_1.Phones, { FIXED } = types_1.Discounts.Kind, { CASH, MANUAL } = types_1.Payments.Method, { MOBILE, HOME } = types_1.Contacts.Type, UBER_PROMO = 'uber_promo', CUTLERY = 'CUTLERY', PICKUP_MAX_WAIT = 30 * 60 * 1000; // ms, 30 mins
class Orders extends providers_1.Base {
    merchantId;
    constructor(provider, merchantId) {
        super(provider);
        this.merchantId = merchantId;
    }
    async accept(orderId, readyAt, externalId) {
        await this.API.acceptOrder(orderId, true, readyAt, externalId);
    }
    /**
     * Enhanced reject method with comprehensive denial reason support and force reject capability
     * @param orderId - Order ID to reject
     * @param options - Rejection options including reason, details, and force reject
     */
    async reject(orderId, options = {}) {
        const { reason = types_2.DenialReason.RESTAURANT_TOO_BUSY, details = 'Unable to fulfill order', forceReject = true, // Default to force reject for reliability
        cancelCode } = options;
        try {
            // First, attempt to deny the order
            await this.API.acceptOrder(orderId, false, undefined, undefined, reason, details);
            if (forceReject) {
                // Convert denial reason to appropriate cancel reason and code
                const cancelReason = types_2.DENIAL_TO_CANCEL_REASON[reason] || types_2.CancelReason.RESTAURANT_TOO_BUSY;
                const defaultCancelCode = types_2.DENIAL_TO_CANCEL_CODE[reason] || 1001;
                const finalCancelCode = cancelCode || defaultCancelCode;
                // Follow up with cancel to ensure order cannot be manually overridden
                // Let api-request handle retries automatically
                try {
                    await this.API.cancelOrder(orderId, cancelReason, finalCancelCode);
                }
                catch (cancelError) {
                    // Cancel might fail if order is already in final state, which is acceptable
                    console.warn(`Cancel after deny failed for order ${orderId}:`, cancelError);
                }
            }
        }
        catch (error) {
            // If deny fails, try direct cancellation as fallback
            console.warn(`Deny failed for order ${orderId}, attempting direct cancel:`, error);
            const cancelReason = types_2.DENIAL_TO_CANCEL_REASON[reason] || types_2.CancelReason.RESTAURANT_TOO_BUSY;
            const defaultCancelCode = types_2.DENIAL_TO_CANCEL_CODE[reason] || 1001;
            const finalCancelCode = cancelCode || defaultCancelCode;
            await this.API.cancelOrder(orderId, cancelReason, finalCancelCode);
        }
    }
    /**
     * Legacy reject method for backward compatibility
     * @deprecated Use reject(orderId, options) instead
     */
    async rejectLegacy(orderId) {
        await this.reject(orderId, {
            reason: types_2.DenialReason.RESTAURANT_TOO_BUSY,
            forceReject: false // Maintain legacy behavior
        });
    }
    /**
     * Cancel order with enhanced reason support
     * @param orderId - Order ID to cancel
     * @param cancelCode - Optional cancel code
     * @param reason - Cancel reason
     */
    async cancel(orderId, cancelCode, reason = types_2.CancelReason.RESTAURANT_TOO_BUSY) {
        await this.API.cancelOrder(orderId, reason, cancelCode);
    }
    /**
     * Force reject an order (deny + cancel) with specific reason
     * @param orderId - Order ID to force reject
     * @param reason - Denial reason
     * @param details - Additional details
     */
    async forceReject(orderId, reason = types_2.DenialReason.RESTAURANT_TOO_BUSY, details) {
        await this.reject(orderId, {
            reason,
            details,
            forceReject: true
        });
    }
    async list(options = {}) {
        const { API, merchantId } = this, { day, next } = options, { data: orders = [], pagination_data } = await API.listOrders(merchantId, day, next), { next_page_token } = pagination_data ?? {};
        return { orders, next: next_page_token };
    }
    async get(id, activeOnly = true) {
        if (activeOnly) {
            return this.API.getOrder(id);
        }
        let next, found = undefined;
        do {
            const res = await this.list({ next }), { orders = [] } = res;
            found = orders.find((o) => o.id === id);
            next = res.next;
        } while (!found && next);
        return found;
    }
    /**
     * Transform UberEats Order to App Order
     */
    toAppOrder(data, options = {}) {
        const { provider } = this, { name } = provider, { id: orderId, display_id: number, payment, store_instructions: note, customers = [], created_time, storeId } = data, { sendReceipt: send = false } = options, { payment_detail, tax_reporting } = payment ?? {}, { order_total, currency_code: currency, promotions } = payment_detail ?? {}, { gross, tax, net, is_tax_inclusive: taxIncluded } = order_total ?? {}, { total: promo_total } = promotions ?? {}, rate = (0, utils_2.taxRate)(tax_reporting), [customer] = customers, { tax_profiles = [] } = customer, [taxProfile] = tax_profiles, { tax_id: taxId } = taxProfile ?? {}, external = {
            [name]: { orderId }
        }, items = this.toMItems(data), amountNative = (0, utils_2.amount2Native)(gross), taxNative = (0, utils_2.amount2Native)(tax), discountAmount = (0, utils_2.amount2Native)(promo_total.gross) * -1, discounts = !discountAmount ? []
            : [{
                    name: UBER_PROMO,
                    kind: FIXED,
                    value: discountAmount
                }], order = {
            receipt: { number, send, taxId },
            currency,
            taxIncluded,
            totalPrice: amountNative,
            totalTax: taxNative,
            items,
            taxes: [{
                    rate,
                    price: taxNative
                }],
            totalDiscounts: discountAmount,
            subtotalPrice: amountNative,
            discounts,
            fulfillment: this.toMFulfillment({ ...data, external, storeId }, items),
            note,
            external,
            storeId,
            createdAt: new Date(created_time)
        };
        return order;
    }
    // ----  To CRM
    /**
     * Transform UberEats Order Items to M Items
     */
    toMItems(order) {
        const { carts, payment } = order, [cart] = carts, { items: lineItems = [] } = cart ?? {}, { payment_detail, tax_reporting } = payment ?? {}, { item_charges } = payment_detail ?? {}, { price_breakdown = [] } = item_charges ?? {}, { breakdown } = tax_reporting ?? {}, { promotions = [] } = breakdown ?? {}, items = [];
        for (const { id: variantId, external_data: sku, quantity: qty, cart_item_id, customer_request } of lineItems) {
            const { total, unit } = price_breakdown.find(p => p.cart_item_id === cart_item_id) ?? {}, { gross_amount: discount } = promotions.find(p => p.instance_id === cart_item_id) ?? {}, { gross: totalGross } = total ?? {}, { gross: unitGross } = unit ?? {}, { amount: quantity = 1 } = qty, { special_instructions } = customer_request ?? {}, options = special_instructions ? [
                { key: 'notes', name: 'Notes', values: [{ title: special_instructions }] }
            ] : [], item = {
                kind: PRODUCT,
                variantId,
                variant: { fulfillmentService: KITCHEN },
                options,
                sku,
                quantity,
                unitPrice: (0, utils_2.amount2Native)(unitGross),
                price: (0, utils_2.amount2Native)(totalGross) + (0, utils_2.amount2Native)(discount),
                discount: {
                    amount: (0, utils_2.amount2Native)(discount) * -1
                }
            };
            items.push(item);
        }
        return items;
    }
    billing(order) {
        const { id: transactionId, payment } = order, { payment_detail } = payment ?? {}, { order_total, currency_code: currency, cash_amount_due } = payment_detail ?? {}, { gross } = order_total ?? {}, { gross: cashPayment } = cash_amount_due ?? {}, amount = (0, utils_2.amount2Native)(gross), method = cashPayment ? CASH : MANUAL;
        return (0, providers_1.manualBilling)(amount, currency, transactionId, method);
    }
    /**
     * Extract customer from order
     */
    customer(order) {
        const { customers } = order, [customer] = customers ?? [];
        if (!customer)
            return;
        const { name, id } = customer, { last_name, first_name, display_name } = name, familyName = (0, utils_1.cleanseName)(last_name), givenName = (0, utils_1.cleanseName)(first_name), fullName = (0, utils_1.cleanseName)(display_name), [phone] = this.phones(order) ?? [], number = typeof phone?.number === 'string' ? phone.number : '', address = { ...this.address(order), number };
        return { id, familyName, givenName, fullName, phone, address };
    }
    phones(order) {
        const { customers } = order, [customer] = customers ?? [];
        if (!customer)
            return [];
        const { contact } = customer, { country_iso2, number: formatted } = contact?.phone ?? {}, { isMobile, valid, countryCode = '', nationalNumber: number = '', fullNumber } = parsePhoneNumber(formatted, country_iso2), phone = valid && isMobile ? { type: MOBILE, countryCode, number, fullNumber } : undefined;
        return phone ? [phone] : [];
    }
    /**
     * Extract destination address from order
     */
    address(order) {
        const { deliveries } = order, [destination] = deliveries ?? [], { location } = destination ?? {}, { unit_number: unit = '', postal_code: postCode, city = '', country = '', latitude = '', longitude = '' } = location ?? {}, { street_address_line_one, street_address_line_two } = location ?? {}, short = [street_address_line_one, street_address_line_two].filter(s => !!s).join(' '), lat = parseInt(latitude), lng = parseInt(longitude), geo = { type: POINT, coordinates: [lng, lat] }, address = { type: HOME, unit, level: '', house: '', street: '', short, postCode, city, country, geo };
        return address;
    }
    /**
     * Transform itemList to CRM FulfillmentList
     */
    toMFulfillment(order, items) {
        const { provider } = this, { name } = provider, { fulfillment_type, customers, deliveries, carts, preparation_time, payment } = order, { external, storeId: placeId, display_id: ticket, store_instructions, created_time } = order, type = (fulfillment_type === PICK_UP)
            ? PICKUP
            : (fulfillment_type === DINE_IN) ? STORE : DELIVER, platformFulfill = fulfillment_type === UBER_DELIVER, [cart] = carts, { include_single_use_items, special_instructions } = cart, { ready_for_pickup_time } = preparation_time, [delivery] = deliveries ?? [], { estimated_pick_up_time = ready_for_pickup_time, estimated_drop_off_time } = delivery ?? {}, { payment_detail } = payment ?? {}, { marketplace_fee_due_to_uber: deliveryFee } = payment_detail, [customer] = customers, status = platformFulfill ? OPEN : PENDING, note = [include_single_use_items ? CUTLERY : '', special_instructions || '', (0, utils_2.cleanse)(store_instructions) || '']
            .filter(Boolean)
            .join(', '), pickup = new Date(estimated_pick_up_time), dropoff = new Date(estimated_drop_off_time || pickup.getTime() + PICKUP_MAX_WAIT), orderedAt = new Date(created_time), fulfillment = {
            type,
            provider: platformFulfill ? name : STORE,
            items,
            price: (0, utils_2.amount2Native)(deliveryFee.net),
            scheduled: {
                minTime: pickup,
                maxTime: dropoff,
            },
            [type]: { ticket, pickup },
            destination: type === DELIVER ? this.address(order) : undefined,
            status,
            note,
            placeId,
            external,
            when: { ordered: orderedAt }
        };
        if (platformFulfill) {
            fulfillment.when.requested = orderedAt;
        }
        if (customer) {
            const { familyName = '', givenName = '', phone } = this.customer(order) ?? {}, { countryCode, number } = phone ?? {}, fullName = `${familyName} ${givenName}`.trim();
            fulfillment.recipient = {
                fullName,
                countryCode,
                phone: number
            };
        }
        return fulfillment;
    }
    /**
     * Transform UberEats Order to CRM Order	// FIXME: Comply with CRM.Order type
     * @param data - UberEats order
     * @param options - paidAt
     */
    toOrder(data, options = {}) {
        // const { provider } = this,
        // 	{ name } = provider,
        // 	{ paidAt } = options,
        // 	{ id: orderId, display_id: number, payment, store_instructions: note, storeId, created_time } = data,
        // 	{ payment_detail } = payment ?? {},
        // 	{ order_total, currency_code: currency, cash_amount_due, promotions } = payment_detail ?? {},
        // 	{ gross, tax, net, is_tax_inclusive: taxIncluded } = order_total ?? {},
        // 	{ gross: dueGross } = cash_amount_due ?? {},
        // 	{ total: promo_total } = promotions ?? {},
        // external = {
        // 	[ name ]: { orderId }
        // },
        // items = this.toItems(data),
        // order: any = {
        // 	receipt: { number },
        // 	currency,
        // 	taxIncluded,
        // 	items,
        // 	taxes: [{
        // 		price: this.amount2Native(tax)
        // 	}],
        // 	discountAmount: this.amount2Native(promo_total.gross),
        // 	subtotalPrice: this.amount2Native(gross),
        // 	amount: this.amount2Native(net),
        // 	when: {
        // 		paid: dueGross ? null : (paidAt || created_time)
        // 	},
        // 	fulfillment: this.toFulfillment({ ...data, external, storeId }, items),
        // 	note,
        // 	external,
        // 	storeId,
        // }
        return {};
    }
    /**
     * Transform UberEats Order Items to CRM Items 	//FIXME: comply with CRM.Item type
     */
    toItems(order) {
        // const { carts, payment } = order,
        // 	[ cart ] = carts,
        // 	{ items: lineItems = [] } = cart ?? {},
        // 	{ payment_detail } = payment ?? {},
        // 	{ item_charges } = payment_detail ?? {},
        // 	{ price_breakdown = [] } = item_charges ?? {},
        // items: any = []
        // for (const { id: variantId, external_data: sku, quantity: qty, cart_item_id, customer_request } of lineItems) {
        // 	const { total, unit } = price_breakdown.find(p => p.cart_item_id === cart_item_id) ?? {},
        // 		{ gross: totalGross } = total ?? {},
        // 		{ gross: unitGross } = unit ?? {},
        // 		{ amount: quantity } = qty,
        // 		{ special_instructions } = customer_request ?? {},
        // 		options = special_instructions ? [
        // 			{ key : 'notes', name: 'Notes', values : [{ title : special_instructions }]}
        // 			] : [],
        // 		item: any = {
        // 			kind: PRODUCT,
        // 			variantId,
        // 			variant: { fulfillmentService: KITCHEN },
        // 			options,
        // 			sku,
        // 			quantity,
        // 			unitPrice: this.amount2Native(unitGross),
        // 			price: this.amount2Native(totalGross)
        // 		}
        // 	items.push(item)
        // }
        return {};
    }
}
exports.Orders = Orders;
//# sourceMappingURL=orders.js.map
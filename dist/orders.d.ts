/**
 * Reference: https://developer.grab.com/docs/grabmart/api/v1-1-3/#tag/submit-order/operation/submit-order-api
 */
import { AppOrders } from '@crm/types';
import { Base, Provider, OrdersInterface, CRM } from '@provider/providers';
import { Order, Address, Phone, ListOptions, OrderList, AppOrderOptions, DenialReason, CancelReason, RejectOptions } from './types';
import { UberSDK } from './sdk';
export declare class Orders extends Base implements OrdersInterface {
    readonly API: UberSDK;
    readonly merchantId: string;
    constructor(provider: Provider, merchantId: string);
    accept(orderId: string, readyAt: Date, externalId?: string): Promise<void>;
    /**
     * Enhanced reject method with comprehensive denial reason support and force reject capability
     * @param orderId - Order ID to reject
     * @param options - Rejection options including reason, details, and force reject
     */
    reject(orderId: string, options?: RejectOptions): Promise<void>;
    /**
     * Legacy reject method for backward compatibility
     * @deprecated Use reject(orderId, options) instead
     */
    rejectLegacy(orderId: string): Promise<void>;
    /**
     * Cancel order with enhanced reason support
     * @param orderId - Order ID to cancel
     * @param cancelCode - Optional cancel code
     * @param reason - Cancel reason
     */
    cancel(orderId: string, cancelCode?: number, reason?: CancelReason): Promise<void>;
    /**
     * Force reject an order (deny + cancel) with specific reason
     * @param orderId - Order ID to force reject
     * @param reason - Denial reason
     * @param details - Additional details
     */
    forceReject(orderId: string, reason?: DenialReason, details?: string): Promise<void>;
    list(options?: ListOptions): Promise<OrderList>;
    get(id: string, activeOnly?: boolean): Promise<Order | void>;
    /**
     * Transform UberEats Order to App Order
     */
    toAppOrder(data: Order, options?: AppOrderOptions): AppOrders.Order;
    /**
     * Transform UberEats Order Items to M Items
     */
    toMItems(order: Order): AppOrders.Item[];
    billing(order: Order): CRM.Billing;
    /**
     * Extract customer from order
     */
    customer(order: Order): CRM.Customer | void;
    phones(order: Order): Phone[];
    /**
     * Extract destination address from order
     */
    address(order: Order): Address;
    /**
     * Transform itemList to CRM FulfillmentList
     */
    protected toMFulfillment(order: Order, items: AppOrders.Item[]): AppOrders.Fulfillment;
    /**
     * Transform UberEats Order to CRM Order	// FIXME: Comply with CRM.Order type
     * @param data - UberEats order
     * @param options - paidAt
     */
    toOrder(data: Order, options?: any): CRM.Order;
    /**
     * Transform UberEats Order Items to CRM Items 	//FIXME: comply with CRM.Item type
     */
    toItems(order: Order): CRM.Item[];
}

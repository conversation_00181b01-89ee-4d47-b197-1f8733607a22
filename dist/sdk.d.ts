import { UberRemote } from './uber-remote';
import { Credentials, Config, MenuConfiguration, MenuType } from './types';
export declare class UberSDK {
    readonly api: UberRemote;
    constructor(credentials: Credentials, options?: Config);
    /**
     * Temporarily pause store
     *	ref:	https://developer.uber.com/docs/eats/references/api/store_suite#tag/SetStoreStatus
     * @param storeId
     * @param isPause - false => resume
     * @param [duration] - '30m', '1h' or '24h' (when isPause = true)
     */
    pauseStore(storeId: string, isPause?: boolean, duration?: string): Promise<void>;
    /**
     * Get Menu
     * 		https://developer.uber.com/docs/eats/references/api/v2/get-eats-stores-storeid-menu
     * @param storeId
     * @param type of menu
     */
    getMenu(storeId: string, type?: MenuType): Promise<MenuConfiguration>;
    /**
     * Upsert Menu
     * 		https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
     * @param storeId
     * @param menu
     */
    updateMenu(storeId: string, menu: MenuConfiguration): Promise<void>;
    /**
     * Remove Menu
     * @param storeId
     * @param menuId
     */
    removeMenu(storeId: string, menuId: string): Promise<void>;
    /**
     * Accept order
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/AcceptOrder/operation/acceptOrder
     * @param orderId
     * @param accept
     * @param readyAt
     */
    acceptOrder(orderId: string, accept?: boolean, readyAt?: Date, external_id?: string): Promise<void>;
    /**
     * Cancel order
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/CancelOrder
     * @param orderId
     * @param cancelCode
     */
    cancelOrder(orderId: string, cancelCode?: number): Promise<void>;
    /**
     * Get active Order
     * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
     * @param orderId
     */
    getOrder(orderId: string): Promise<any>;
    /**
     * List of orders for day
     * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
     * @param storeId
     * @param day - not supported for now
     * @param next_page_token
     */
    listOrders(storeId: string, day?: Date, next_page_token?: string): Promise<any>;
    /**
     * Mark order as Ready for delivery or completed for dine-in
     * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/OrderReady/operation/orderReady
     * @param orderId
     * @param markStatus 1 = ready (for delivery), 2 = completed (dine-in)
     */
    orderReady(orderId: string, markStatus?: number): Promise<void>;
    verifyWebhook(headers: any, body: string | any): boolean;
    private handleError;
}

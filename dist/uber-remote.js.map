{"version": 3, "file": "uber-remote.js", "sourceRoot": "", "sources": ["../src/uber-remote.ts"], "names": [], "mappings": ";;;AAAA,6CAAwC;AACxC,oDAAgF;AAChF,mCAAsD;AAEtD,MAAM,SAAS,GAAG,OAAO,CAAC,uBAAuB,CAAC,EACjD,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,qBAAO,EACzC,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,uBAAS,EAC1C,EAAE,kBAAkB,EAAE,GAAG,mBAAK,CAAC,SAAS,EACxC,EAAE,IAAI,EAAE,GAAG,eAAO,EAClB,IAAI,GAAG,MAAM,CAAA;AAEd,MAAa,UAAW,SAAQ,uBAAS;IAEhC,WAAW,CAAa;IAEhC,YAAY,WAAwB,EAAE,WAAmB,EAAE;QAC1D,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,WAAW,EAC1D,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,CAAA;QAEnC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU;YAAE,MAAM,wBAAwB,CAAA;QAEjF,KAAK,CAAC,eAAe,GAAG,IAAI,CAAA;QAC5B,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,CAAA;QAE7C,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;QACzB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAE9B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1C,CAAC,MAAW,EAAE,EAAE;YACf,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAA;YAE5B,IAAI,WAAW,EAAE,CAAC;gBACjB,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,UAAU,WAAW,EAAE,CAAA;YACxD,CAAC;YACD,OAAO,MAAM,CAAA;QACd,CAAC,CACD,CAAA;QACD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAK,CAAC,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,EAAO,EAAE,EAAE;YAChF,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,EAAE,CAAA;YACpB,CAAC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;IAED,IAAI,WAAW;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAA;IACpC,CAAC;IAED,IAAc,MAAM;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA;IAC/B,CAAC;IAED,IAAc,QAAQ;QACrB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAA;IACjC,CAAC;IAED,IAAc,KAAK;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA;IAC9B,CAAC;IAED,KAAK,CAAC,YAAY;QACjB,IAAI,CAAC;YACJ,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG,IAAI,EACjE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EACpB,OAAO,GAAG,EAAE,CAAE,YAAY,CAAE,EAAE,eAAe,EAAE,EAC/C,UAAU,GAAG,kBAAkB,EAC/B,IAAI,GAAG,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE,EACtD,WAAW,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,EACrD,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,WAAW,IAAI,EAAE,CAAA;YAEjD,IAAI,YAAY,EAAE,CAAC;gBAClB,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,YAAY,CAAA;gBAC3C,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,IAAI,CAAC,CAAA;YACtE,CAAC;QACF,CAAC;QACD,OAAO,GAAQ,EAAE,CAAC;YACjB,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,GAAG,EAC3B,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,CAAA;YAE5B,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;QAC7B,CAAC;IACF,CAAC;IAED,SAAS,CAAC,OAAe;QACxB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,EACtB,SAAS,GAAG,IAAA,wBAAU,EAAC,QAAQ,EAAE,MAAM,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC;aACf,MAAM,CAAC,KAAK,CAAC,CAAA;QAEhB,OAAO,SAAS,CAAA;IACjB,CAAC;IAES,QAAQ,CAAC,OAAe;QACjC,MAAM,EAAE,GAAG,GAAG,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,EACjC,CAAE,UAAU,CAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAElC,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAA;IAC3C,CAAC;CACD;AAvFD,gCAuFC"}
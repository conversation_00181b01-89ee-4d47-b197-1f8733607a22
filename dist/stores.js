"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Stores = void 0;
const providers_1 = require("@provider/providers");
const DURATION = ['30m', '1h', '24h'];
class Stores extends providers_1.Base {
    merchantId;
    constructor(provider, merchantId) {
        super(provider);
        this.merchantId = merchantId;
    }
    /**
     * @param merchantId
     * @param duration - 1 = 30m, 2 = 1h, 3 = 24h
     */
    async pause(merchantId = this.merchantId, duration) {
        const durationStr = duration ? DURATION[duration - 1] || undefined : undefined;
        await this.API.pauseStore(merchantId, true, durationStr).catch(() => null);
    }
    async resume(merchantId = this.merchantId) {
        await this.API.pauseStore(merchantId, false).catch(() => null);
    }
}
exports.Stores = Stores;
//# sourceMappingURL=stores.js.map
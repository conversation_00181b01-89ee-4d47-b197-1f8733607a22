import { Base, Provider } from '@provider/providers';
import { UberSDK } from './sdk';
export declare class Stores extends Base {
    readonly API: UberSDK;
    readonly merchantId: string;
    constructor(provider: Provider, merchantId: string);
    /**
     * @param merchantId
     * @param duration - 1 = 30m, 2 = 1h, 3 = 24h
     */
    pause(merchantId?: string, duration?: number): Promise<void>;
    resume(merchantId?: string): Promise<void>;
}

import { ApiRemote } from '@perkd/api-request';
import { Credentials, Config } from './types';
export declare class UberRemote extends ApiRemote {
    private credentials;
    constructor(credentials: Credentials, settings?: Config);
    get accessToken(): string | undefined;
    protected get secret(): string;
    protected get clientId(): string;
    protected get scope(): import("./types").Scope;
    refreshToken(): Promise<void>;
    signature(content: string): string;
    protected endpoint(service: string): any;
}

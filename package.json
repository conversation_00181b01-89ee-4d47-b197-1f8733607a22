{"version": "0.8.1", "name": "@perkd/uber-sdk", "description": "SDK for UberEats", "author": "<EMAIL>", "license": "MIT", "engines": {"node": ">=20"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "yarn run build", "start": "node dist/index.js", "test": "tsx --test --require dotenv/config tests/**/*.test.ts", "test-one": "tsx --test --require dotenv/config", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc"}, "homepage": "https://github.com/perkd/uber-sdk#readme", "bugs": {"url": "https://github.com/perkd/uber-sdk/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/uber-sdk.git"}, "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"@crm/types": "github:perkd/crm-types#semver:^1.10.24", "@perkd/api-request": "github:perkd/api-request#semver:^2.0.1", "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0", "@perkd/utils": "github:perkd/utils#semver:^2.0.0", "@provider/providers": "github:perkd/provider-providers#semver:^1.7.0", "striptags": "^3.2.0"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@types/node": "^22.15.3", "dotenv": "^16.5.0", "eslint-plugin-mocha": "^11.0.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.1"}
{"version": "0.8.2", "name": "@perkd/uber-sdk", "description": "SDK for UberEats", "author": "<EMAIL>", "license": "MIT", "engines": {"node": ">=20"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "prestart": "yarn run build", "start": "node dist/index.js", "test": "tsx --test --require dotenv/config tests/**/*.test.ts", "test-one": "tsx --test --require dotenv/config", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc"}, "homepage": "https://github.com/perkd/uber-sdk#readme", "bugs": {"url": "https://github.com/perkd/uber-sdk/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/perkd/uber-sdk.git"}, "files": ["README.md", "dist", "!*/__tests__", "!tsconfig.tsbuildinfo"], "dependencies": {"@crm/types": "github:perkd/crm-types#semver:^1.11.19", "@perkd/api-request": "github:perkd/api-request#semver:^2.0.2", "@perkd/metrics": "github:perkd/metrics#semver:^1.8.0", "@perkd/utils": "github:perkd/utils#semver:^2.0.5", "@provider/providers": "github:perkd/provider-providers#semver:^1.7.4", "striptags": "^3.2.0"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@types/node": "^24.0.10", "dotenv": "^17.0.1", "eslint-plugin-mocha": "^11.1.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.1"}
import assert from 'node:assert'
import { test } from 'node:test'
import { UberRemote } from '../src/uber-remote'
import { CREDENTIALS, OPTIONS } from './env'

test('UberRemote', async (t) => {
	const remote = new UberRemote(CREDENTIALS, OPTIONS)

	await t.test('should get AccessToken', async () => {
		const { accessToken: oldToken } = remote

		await remote.refreshToken()

		assert.notEqual(remote.accessToken, oldToken)
		assert.equal(!!remote.accessToken, true)
	})
})

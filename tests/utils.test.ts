import assert from 'node:assert'
import { test } from 'node:test'
import { duration2Until } from '../src/utils'

const ONE_DAY = 24 * 60 * 60 * 1000,		// milliseconds
	ONE_YEAR = ONE_DAY * 365,
	NOW = new Date().getTime(),
	defaultStart = new Date(NOW - ONE_YEAR),
	defaultEnd = new Date(NOW + ONE_YEAR)

defaultStart.setUTCHours(16, 0, 0, 0)
defaultEnd.setUTCHours(15, 59, 59, 0)

test('Utils', async (t) => {

	await t.test('duration2Until', async function() {
		const now = Date.now()

		// Test 30 minutes duration
		const minute30 = duration2Until('30m')
		const minute30Date = new Date(minute30)
		const minute30Expected = new Date(now + 30 * 60000)
		assert.ok(Math.abs(minute30Date.getTime() - minute30Expected.getTime()) < 1000, '30m should be approximately 30 minutes in the future')

		// Test 1 hour duration
		const hour1 = duration2Until('1h')
		const hour1Date = new Date(hour1)
		const hour1Expected = new Date(now + 60 * 60000)
		assert.ok(Math.abs(hour1Date.getTime() - hour1Expected.getTime()) < 1000, '1h should be approximately 1 hour in the future')

		// Test 24 hours (1 day) duration
		const day1 = duration2Until('24h')
		const day1Date = new Date(day1)
		const day1Expected = new Date(now + 24 * 60 * 60000)
		assert.ok(Math.abs(day1Date.getTime() - day1Expected.getTime()) < 1000, '24h should be approximately 24 hours in the future')

		// Test default value (1h)
		const defaultDuration = duration2Until()
		const defaultDate = new Date(defaultDuration)
		const defaultExpected = new Date(now + 60 * 60000)
		assert.ok(Math.abs(defaultDate.getTime() - defaultExpected.getTime()) < 1000, 'Default should be approximately 1 hour in the future')

		// Verify return format is ISO string
		assert.ok(minute30.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/), 'Should return ISO format string')
	})
})
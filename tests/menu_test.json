{"menus": [{"id": "All-day", "title": {"translations": {"en": "All Day"}}, "service_availability": [{"day_of_week": "monday", "time_periods": [{"start_time": "08:00", "end_time": "21:59"}]}, {"day_of_week": "tuesday", "time_periods": [{"start_time": "08:00", "end_time": "21:59"}]}, {"day_of_week": "wednesday", "time_periods": [{"start_time": "08:00", "end_time": "21:59"}]}, {"day_of_week": "thursday", "time_periods": [{"start_time": "08:00", "end_time": "21:59"}]}, {"day_of_week": "friday", "time_periods": [{"start_time": "08:00", "end_time": "21:59"}]}, {"day_of_week": "saturday", "time_periods": [{"start_time": "00:00", "end_time": "23:59"}]}, {"day_of_week": "sunday", "time_periods": [{"start_time": "00:00", "end_time": "23:59"}]}], "category_ids": ["Sandwiches", "Snacks", "Drinks"]}], "categories": [{"id": "Drinks", "title": {"translations": {"en": "Drinks"}}, "entities": [{"id": "Coffee"}, {"id": "Tea"}]}, {"id": "Snacks", "title": {"translations": {"en": "Snacks"}}, "entities": [{"id": "<PERSON><PERSON>"}]}, {"id": "Sandwiches", "title": {"translations": {"en": "Sandwiches"}}, "entities": [{"id": "Chicken-sandwich"}]}], "items": [{"id": "Milk", "external_data": "External data for milk", "title": {"translations": {"en": "Milk"}}, "description": {"translations": {"en": ""}}, "price_info": {"price": 0, "overrides": []}, "quantity_info": {"overrides": [{"context_type": "MODIFIER_GROUP", "context_value": "Add-milk", "quantity": {"max_permitted": 1}}]}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Chocolate-deluxe", "external_data": "External data for chocolate deluxe flavor", "title": {"translations": {"en": "Chocolate deluxe"}}, "description": {"translations": {"en": ""}}, "price_info": {"price": 50, "overrides": []}, "quantity_info": {"overrides": [{"context_type": "MODIFIER_GROUP", "context_value": "Choose-flavor", "quantity": {"max_permitted": 1}}]}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Sugar", "external_data": "External data for sugar", "title": {"translations": {"en": "Sugar"}}, "description": {"translations": {"en": ""}}, "price_info": {"price": 0, "overrides": []}, "quantity_info": {"overrides": [{"context_type": "MODIFIER_GROUP", "context_value": "Add-sugar", "quantity": {"max_permitted": 2}}]}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Chicken-sandwich", "external_data": "External data for chicken sandwich", "title": {"translations": {"en": "Chicken sandwich"}}, "description": {"translations": {"en": "Whole grain bread, grilled chicken and salad"}}, "price_info": {"price": 700, "overrides": []}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Blueberry", "external_data": "External data for blueberry flavor", "title": {"translations": {"en": "Blueberry"}}, "description": {"translations": {"en": ""}}, "price_info": {"price": 0, "overrides": []}, "quantity_info": {"overrides": [{"context_type": "MODIFIER_GROUP", "context_value": "Choose-flavor", "quantity": {"max_permitted": 1}}]}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Tea", "external_data": "External data for tea", "title": {"translations": {"en": "Tea"}}, "description": {"translations": {"en": "A soothing cuppa"}}, "price_info": {"price": 250, "overrides": []}, "modifier_group_ids": {"ids": ["Add-milk", "Add-sugar"], "overrides": []}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "<PERSON><PERSON>", "external_data": "External data for muffin", "title": {"translations": {"en": "Fresh-baked muffin"}}, "description": {"translations": {"en": "Great for afternoon snack time!"}}, "price_info": {"price": 300, "overrides": []}, "modifier_group_ids": {"ids": ["Choose-flavor"], "overrides": []}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}, {"id": "Coffee", "external_data": "External data for coffee", "title": {"translations": {"en": "Coffee"}}, "description": {"translations": {"en": "Deliciously roasted beans"}}, "price_info": {"price": 310, "overrides": []}, "suspension_info": {"suspension": {"suspend_until": 1710431999}, "overrides": []}, "modifier_group_ids": {"ids": ["Add-milk", "Add-sugar"], "overrides": []}, "tax_info": {"tax_rate": 8}, "nutritional_info": {"allergens": null}, "dish_info": {"classifications": {"ingredients": null, "additives": null}}, "product_info": {"product_traits": null, "countries_of_origin": null}, "bundled_items": null}], "modifier_groups": [{"id": "Add-milk", "external_data": "External data for milk choice", "title": {"translations": {"en": "Add milk"}}, "quantity_info": {"quantity": {"max_permitted": 1}, "overrides": []}, "modifier_options": [{"id": "Milk", "type": "ITEM"}]}, {"id": "Choose-flavor", "external_data": "External data for muffin flavor choice", "title": {"translations": {"en": "Choose flavor"}}, "quantity_info": {"quantity": {"min_permitted": 1, "max_permitted": 1}, "overrides": []}, "modifier_options": [{"id": "Blueberry", "type": "ITEM"}, {"id": "Chocolate-deluxe", "type": "ITEM"}]}, {"id": "Add-sugar", "external_data": "External data for sugar choice", "title": {"translations": {"en": "Add sugar"}}, "quantity_info": {"quantity": {"max_permitted": 2}, "overrides": []}, "modifier_options": [{"id": "Sugar", "type": "ITEM"}]}]}
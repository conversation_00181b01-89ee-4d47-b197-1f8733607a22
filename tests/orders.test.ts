process.env.NODE_ENV = 'test'

import assert from 'node:assert'
import { test } from 'node:test'
import { UberSDK } from '../src/sdk'
import { CREDENTIALS, MERCHANT_ID } from './env'

/**
 * Create 2 new orders with UberEat Dev Console before each test
 * */
let orderId: string

test('Order', async (t) => {

	const uber = new UberSDK(CREDENTIALS)

	await t.test('should list all orders & get first offered order', async() => {
		const { data } = await uber.listOrders(MERCHANT_ID),
			ORDER = data.find((order: any) => order.state === 'OFFERED')

		if (!ORDER) {
			console.log('⚠️ ⚠️ ⚠️  No offered orders found, please create new test orders via UberEats TEST account')
			return
		}
			
		const { id, status, display_id, state } = ORDER

		console.log(`state: order#${display_id}: ${state}`)

		orderId = id

		assert(data.length > 1, 'result has more multiple orders')
		assert.equal(status, 'ACTIVE')
		assert.equal(state, 'OFFERED')
	})

	await t.test('shoud get order detail', async() => {
		if (!orderId) {
			console.log('⚠️ ⚠️ ⚠️  No offered orders found, please create new test orders via UberEats TEST account')
			return
		}
		const order = await uber.getOrder(orderId),
			{ id, display_id, store, payment, customers, preparation_time } = order,
			requiredProps = { display_id, store, payment, customers, preparation_time }

		for (const [key, value] of Object.entries(requiredProps)) {
			assert.ok(value, `${key} should not be empty`);
		}

		assert.equal(id, orderId)
	})

	await t.test('should accept order', async() => {
		if (!orderId) {
			console.log('⚠️ ⚠️ ⚠️  No offered orders found, please create new test orders via UberEats TEST account')
			return
		}
		await uber.acceptOrder(orderId)
		const order = await uber.getOrder(orderId),
			{ state, display_id } = order

		console.log(`state: order#${display_id}: ${state}`)
		assert.equal(state, 'ACCEPTED')
	})

	await t.test('should cancel order', async() => {
		if (!orderId) {
			console.log('⚠️ ⚠️ ⚠️  No offered orders found, please create new test orders via UberEats TEST account')
			return
		}
		await uber.cancelOrder(orderId)
		// there's time lag between cancelling & retrieving the cancelled order data, causing ERR404 if getOrder(orderId)
	})

	await t.test('should reject order', async() => {
		const { data } = await uber.listOrders(MERCHANT_ID),
			ORDER = data.find((order: any) => order.state === 'OFFERED')

			if (!ORDER) {
				console.log('⚠️ ⚠️ ⚠️  No offered orders found, please create new test orders via UberEats TEST account')
				return
			}
			const { id } = ORDER
			await uber.acceptOrder(id, false)
	})
})

// test('SDK - Orders (TEST)', async (t) => {
// 	const sdk = new UberSDK(CREDENTIALS, OPTIONS)

// 	await t.test('List Orders', async (t) => {
// 		const { data, pagination_data } = await sdk.listOrders(MERCHANT_ID),
// 			{ page_size } = pagination_data ?? {},
// 			[ first = {} ] = data,
// 			{ id, status, state, customers } = first,
// 			[ customer = {} ] = customers,
// 			{ name, contact = {} } = customer,
// 			{ first_name, last_name, display_name } = name,
// 			{ phone = {} } = contact,
// 			{ country_iso2, number, pin_code } = phone

// 		DATA.orderId = id
// 		console.log(phone)

// 		assert.equal(status, 'COMPLETED')
// 		assert.equal(state, 'OFFERED')
// 	})

	// await t.test('Get Order (active only)', async (t) => {
	// 	const order = await sdk.getOrder(ORDER_ID)

	// 	assert.equal(!!order, true)
	// })

	// await t.test('Accept & Deliver', async(t) => {
	// 	const { orderId } = DATA

	// 	await t.test('should accept', async () => {
	// 		await sdk.acceptOrder(orderId)
	// 	})
	// 	await t.test('should ready', async () => {
	// 		await sdk.orderReady(orderId)
	// 	})
	// 	await t.test('should collected', async () => {
	// 		await sdk.orderCollected(GRAB_ORDER_ID)
	// 	})
	// 	await t.test('should delivered', async () => {
	// 		await sdk.orderDelivered(GRAB_ORDER_ID)
	// 	})
	// })

	// await t.test('Reject Order', async (t) => {
	// 	const { orderId } = DATA

	// 	await sdk.acceptOrder(orderId, false)
	// })

	// await t.test('Cancel Order', async (t) => {
	// 	const { orderId } = DATA

	// 	await sdk.cancelOrder(orderId, MERCHANT_ID, 1002)
	// })
// })

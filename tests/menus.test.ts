process.env.NODE_ENV = 'test'

import assert from 'node:assert'
import { test } from 'node:test'
import { UberSDK, MenuType, Products, Product, Variant, Place } from '../src'
import { CREDENTIALS, MERCHANT_ID, PROVIDER, PRODUCTS2 as ENV_PRODUCTS2, PRODUCTS3 as ENV_PRODUCTS3, VARIANTS as ENV_VARIANTS } from './env'

const { PICKUP, DELIVER } = MenuType,
	MENU = require('./menu_ichigyu.json')

// Define placeholders for missing constants (replace with actual values)
const VARIANTS: Variant[] = ENV_VARIANTS as any; // Use Variant type
const PLACE: Place = {} as Place // Use Place type and assertion
const MENU_ID: string = 'default-menu-id' // Placeholder
const LANGUAGE: string = 'en-US' // Placeholder

// Cast ENV_PRODUCTS to Product[] to satisfy the type checker for now
const PRODUCTS2: Product[] = ENV_PRODUCTS2 as any;
const PRODUCTS3: Product[] = ENV_PRODUCTS3 as any;

// Cast PROVIDER to any to satisfy the constructor for now
const typedProvider: any = PROVIDER;

test('SDK - Menus', async (t) => {
	
	const uber = new UberSDK(CREDENTIALS)

	await t.test('should get DELIVER menu', async() => {
		const MenuConfig = await uber.getMenu(MERCHANT_ID, DELIVER),
			{ menus, categories, items } = MenuConfig

		assert.notEqual(menus.length, 0)
		assert.notEqual(categories.length, 0)
		assert.notEqual(items.length, 0)
	})

	await t.test('should remove menu', async() => {
		const { id } = MENU.menus[0]
		await uber.removeMenu(MERCHANT_ID, id)

		const MenuConfig = await uber.getMenu(MERCHANT_ID),
			{ categories, items, modifier_groups } = MenuConfig

		assert.equal(categories, null)
		assert.equal(items, null)
		assert.equal(modifier_groups, null)

	})

	await t.test('should upload menu', async () => {
		await uber.updateMenu(MERCHANT_ID, MENU)
		const MenuConfig = await uber.getMenu(MERCHANT_ID, DELIVER)
	})

	await t.test('Should throw error because of category missing', async () => {
		// Instantiate Products using the casted provider
		const productsHandler = new Products(typedProvider, MERCHANT_ID)
        // Add assertions
        assert.throws(
            () => productsHandler.fromProduct(PRODUCTS2, VARIANTS, PLACE, MENU_ID, LANGUAGE),
            {
                message: 'Product id: 66c52133ead804a28acf0c35 must have a category'
            },
            'Should throw error when product has no category'
        )
	})
	
	await t.test('should transform products to menu', async () => {
		// Instantiate Products using the casted provider
		const productsHandler = new Products(typedProvider, MERCHANT_ID)
		// Call fromProduct on the instance using casted PRODUCTS
		// NOTE: fromProduct is synchronous based on the previous search results, removed await
		const menu = productsHandler.fromProduct(PRODUCTS3, VARIANTS, PLACE, MENU_ID, LANGUAGE) 
		console.log(JSON.stringify(menu, null, 2)) // Optional: log the generated menu
		// Add assertions
		assert.ok(menu, 'Menu should be generated');
        assert.ok(menu.menus.length > 0, 'Should have at least one menu entry');
        assert.ok(menu.categories.length > 0, 'Should have at least one category');
	})

})

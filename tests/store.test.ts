process.env.NODE_ENV = 'test'

import { test } from 'node:test'
import { UberSDK } from '../src/sdk'
import { CREDENTIALS, MERCHANT_ID } from './env'

test('Store', async (t) => {
	const sdk = new UberSDK(CREDENTIALS)

	await t.test('should pause (30m)', async () => {
		await sdk.pauseStore(MERCHANT_ID, true, '30m')
	})

	await t.test('should resume', async () => {
		await sdk.pauseStore(MERCHANT_ID, false)
	})
})

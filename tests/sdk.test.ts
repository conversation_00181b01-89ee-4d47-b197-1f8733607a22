import assert from 'node:assert'
import { test } from 'node:test'
import { UberSDK } from '../src/sdk'
import { CREDENTIALS } from './env'

const TESTS = [
	{
		headers: {
			'x-uber-signature': '7633ddae3577310e77997fcc255b2466fdd4c505264a3270c2edf69335c2947f',
		},
		body: "{\n  \"event_id\": \"f5274b35-0110-4921-b165-e3c8639698e0\",\n  \"event_time\": 1721730725925,\n  \"event_type\": \"delivery.state_changed\",\n  \"meta\": {\n    \"courier_trip_id\": \"226a95e0-77e2-4609-b0bf-75a04721fe8e\",\n    \"external_order_id\": \"\",\n    \"order_id\": \"b7211815-ce43-484d-b4b6-c4d9cc9f72fb\",\n    \"status\": \"EN_ROUTE_TO_PICKUP\",\n    \"store_id\": null\n  },\n  \"resource_href\": \"https://api.uber.com/v1/delivery/order/b7211815-ce43-484d-b4b6-c4d9cc9f72fb\",\n  \"webhook_meta\": {\n    \"client_id\": \"T_HzBzABVuTUtm1QgtG9bKMHXFtUk0UP\",\n    \"webhook_config_id\": \"eats-restaurant-order-experience.delivery-status-webhooks\",\n    \"webhook_msg_timestamp\": 1721730728,\n    \"webhook_msg_uuid\": \"efc016c4-3195-4683-a8ef-2f9cb0c190cb\"\n  }\n}"
	}
]

test('SDK', async (t) => {
	const sdk = new UberSDK(CREDENTIALS)

	await t.test('should verifyWebhook', async () => {
		const [ test ] = TESTS,
		 { headers, body } = test

		assert.notEqual(sdk.verifyWebhook(headers, body), true)
	})
})

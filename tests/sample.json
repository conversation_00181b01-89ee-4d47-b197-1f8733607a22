{"orders": {"cashOrder": {"id": "33ebd699-552d-448a-b3f0-d9c1c588ed9d", "display_id": "8ED9D", "state": "OFFERED", "ordering_platform": "UBER_EATS", "store": {"id": "4ac7d228-d32a-4b02-9967-29948e375ef7", "name": "Grocohol Test Store", "timezone": "Asia/Taipei", "partner_identifiers": [{"value": "4ac7d228-d32a-4b02-9967-29948e375ef7", "type": "MERCHANT_STORE_ID"}, {"value": "so9VDkbUey32IzwBMK9mfNJYlBMLk-Ud", "type": "ORDER_MANAGER_CLIENT_ID"}, {"value": "Perkd Store", "type": "INTEGRATOR_STORE_ID"}, {"value": "Perkd", "type": "INTEGRATOR_BRAND_ID"}]}, "customers": [{"id": "eee1310a-b8d5-5949-a6b6-065b1e1ac3b6", "name": {"display_name": "Grocohol T.", "first_name": "Grocohol", "last_name": "T."}, "order_history": {"past_order_count": 0}, "is_primary_customer": true, "contact": {"phone": {"country_iso2": "SG", "number": "+6597812929"}}, "can_respond_to_fulfillment_issues": true}], "deliveries": [{"id": "510cd518-46ca-4007-9170-0b5d70f55c85", "status": "SCHEDULED", "estimated_pick_up_time": "2024-08-15T19:47:40+08:00", "interaction_type": "DELIVER_TO_DOOR", "estimated_drop_off_time": "2024-08-15T20:10:00+08:00"}], "carts": [{"id": "07129a80-8094-4c96-ae10-7b8bc8ad94c2", "revision_id": "759ee86f-44f0-589b-a821-c9c710e6eec8", "items": [{"id": "6688117317325bff89a0f532", "cart_item_id": "78f83668-cff0-4e79-a58f-218835e4edab", "customer_id": "eee1310a-b8d5-5949-a6b6-065b1e1ac3b6", "external_data": "bento-2", "title": "醬燒牛五花溫玉丼", "quantity": {"amount": 1, "unit": "PIECE"}, "picture_url": "https://tb-static.uber.com/prod/image-proc/processed_images/e64d0ff2fe4f565fffe495707b910221/5143f1e218c67c20fe5a4cd33d90b07b.jpeg"}], "include_single_use_items": false, "restricted_items": {"alcohol": {"contain_alcoholic_item": false}}}], "payment": {"payment_detail": {"order_total": {"net": {"amount_e5": 9500000, "currency_code": "TWD", "formatted": "NT$95.00"}, "tax": {"amount_e5": 500000, "currency_code": "TWD", "formatted": "NT$5.00"}, "gross": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "display_amount": "NT$100.00", "is_tax_inclusive": true}, "item_charges": {"total": {"net": {"amount_e5": 9500000, "currency_code": "TWD", "formatted": "NT$95.00"}, "tax": {"amount_e5": 500000, "currency_code": "TWD", "formatted": "NT$5.00"}, "gross": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "display_amount": "NT$100.00", "is_tax_inclusive": true}, "price_breakdown": [{"cart_item_id": "78f83668-cff0-4e79-a58f-218835e4edab", "price_type": "ITEM", "quantity": {"amount": 1, "unit": "PIECE"}, "total": {"gross": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "display_amount": "NT$100.00"}, "unit": {"gross": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "display_amount": "NT$100.00"}}]}, "fees": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}}, "promotions": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}, "order_total_excluding_promos": {"net": {"amount_e5": 9500000, "currency_code": "TWD", "formatted": "NT$95.00"}, "tax": {"amount_e5": 500000, "currency_code": "TWD", "formatted": "NT$5.00"}, "gross": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "display_amount": "NT$100.00", "is_tax_inclusive": true}}, "cash_amount_due": {"gross": {"amount_e5": 16000000, "currency_code": "TWD", "formatted": "NT$160.00"}, "display_amount": "NT$160.00"}, "currency_code": "TWD", "marketplace_fee_due_to_uber": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}}, "tax_reporting": {"breakdown": {"items": [{"instance_id": "78f83668-cff0-4e79-a58f-218835e4edab", "description": "ITEM", "gross_amount": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "net_amount": {"amount_e5": 9523809, "currency_code": "TWD", "formatted": "NT$95.24"}, "total_tax": {"amount_e5": 476190, "currency_code": "TWD", "formatted": "NT$4.76"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 476190, "currency_code": "TWD", "formatted": "NT$4.76"}, "taxable_amount": {"amount_e5": 10000000, "currency_code": "TWD", "formatted": "NT$100.00"}, "tax_remittance": "MERCHANT"}]}], "fees": [{"instance_id": "78f83668-cff0-4e79-a58f-218835e4edab", "description": "SERVICE_FEE", "gross_amount": {"amount_e5": 500000, "currency_code": "TWD", "formatted": "NT$5.00"}, "net_amount": {"amount_e5": 476190, "currency_code": "TWD", "formatted": "NT$4.76"}, "total_tax": {"amount_e5": 23810, "currency_code": "TWD", "formatted": "NT$0.24"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 23810, "currency_code": "TWD", "formatted": "NT$0.24"}}]}, {"instance_id": "78f83668-cff0-4e79-a58f-218835e4edab", "description": "DELIVERY_FEE", "gross_amount": {"amount_e5": 5500000, "currency_code": "TWD", "formatted": "NT$55.00"}, "net_amount": {"amount_e5": 5238100, "currency_code": "TWD", "formatted": "NT$52.38"}, "total_tax": {"amount_e5": 261900, "currency_code": "TWD", "formatted": "NT$2.62"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 261900, "currency_code": "TWD", "formatted": "NT$2.62"}}]}]}}, "tender_types": null}, "status": "ACTIVE", "estimated_unfulfilled_at": "2024-08-15T19:56:40+08:00", "is_order_accuracy_risk": false, "preparation_time": {"ready_for_pickup_time_secs": 150, "source": "PREDICTED_BY_UBER", "ready_for_pickup_time": "2024-08-15T19:47:40+08:00"}, "action_eligibility": {"adjust_ready_for_pickup_time": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "mark_out_of_item": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "cancel": {"is_eligible": false, "reason": "ORDER_NOT_ACCEPTED"}, "mark_cannot_fulfill": {"is_eligible": false, "reason": "NO_ORDER_INSTRUCTIONS"}}, "fulfillment_type": "DELIVERY_BY_UBER", "created_time": "2024-08-15T19:45:10+08:00", "has_membership_pass": false}}}
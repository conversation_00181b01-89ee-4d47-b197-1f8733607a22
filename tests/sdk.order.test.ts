import assert from 'node:assert'
import { test } from 'node:test'
import { Orders } from '../src/orders'
import { PROVIDER } from './env'

const DATA = require('./sample.json'),
    { cashOrder } = DATA.orders

test('ORDERS', async(t) => {

    // change to constructor(provider: any) in src/orders.ts before start
    const typedProvider: any = PROVIDER;
    const sdk = new Orders(typedProvider, 'merchantId')

    await t.test('billing', async(t) => {
        const billing = sdk.billing(cashOrder)
        console.log(JSON.stringify(billing))
    })
})


// ENVIRONMENT
const {
	UBER_CLIENT_ID,
	UBER_SECRET,
} = process.env

export const
	{ MERCHANT_ID } = process.env,
	CREDENTIALS: any = {
		clientId: UBER_CLIENT_ID,
		secret: UBER_SECRET,
		merchantId: MERCHANT_ID,
		scope: 'eats.store eats.order eats.store.status.write eats.store.orders.cancel eats.store.orders.read eats.store.status.read',
	},
	OPTIONS = {
		liveMode: false,
	},
	PROVIDER = {
		name: 'ubereats',
		services: [ 'product', 'order'],
		credentials: CREDENTIALS,
		liveMode: false,
		enabled: true,
	},
	PRODUCTS = [
		{
			id: "650e5bb1ea364780e9a66c3e",
			title: "Mao Shan Wang Durian Ice Cream Pint",
			sku: "durian-msw",
			gtin:{
				value: "012345678905"
			},
			position: 1,
			weight: {
			  value: 3.9,
			  unit: "kg",
			  count: 2
			},
			inventory: {
			  management: "shopify",
			  policy: "deny",
			  lowQuantityWarningThreshold: 0,
			  quantity: 316
			},
			prices: [
				{
					name: 'base',
					price: {
						value: 10,
						currency: 'SGD'
					}
				}
			],
			description: "Actual Mao Shan Wang puree blended in to achieve our signature rich and creamy ice cream. Perfect dessert for any time.",
			tags: [
				"durian",
				"dessert"
			],
			imageUrls: [
			  "https://cdn.shopify.com/s/files/1/0584/5988/1641/products/3.MeasurementChart_0036a0dd-e661-4038-8fbb-d7cdfb964384.jpg?v=**********"
			]
		  }
	],
	PRODUCTS2 = [
		{
			"title": "炙燒雪花豚丼(不含蛋)",
			"description": "",
			"brand": "虛擬店",
			"availability": "active",
			"isLowQuantity": false,
			"isSoldOut": false,
			"isBackOrder": false,
			"external": {
			  "shopify": {
				"productId": "7384279416918",
				"shop": "ichigyu-test.myshopify.com"
			  }
			},
			"links": [],
			"tags": {
			  "category": [
				""
			  ],
			  "kitchen": [
				"主廚"
			  ]
			},
			"behaviors": {},
			"visible": true,
			"globalize": {
			  "t": {
				"zh-Hant": {
				  "title": "炙燒雪花豚丼(不含蛋)",
				  "brand": "虛擬店"
				}
			  },
			  "default": "zh-Hant"
			},
			"createdAt": "2024-08-20T22:11:06.000Z",
			"modifiedAt": "2025-04-20T17:25:27.000Z",
			"deletedAt": null,
			"id": "66c52133ead804a28acf0c35",
			"variationList": [],
			"bundleList": []
		  }
	],
	PRODUCTS3 = [
		{
			"title": "和牛漢堡排御膳",
			"description": "",
			"brand": "高雄博愛店",
			"availability": "active",
			"isLowQuantity": false,
			"isSoldOut": false,
			"isBackOrder": false,
			"external": {},
			"links": [],
			"tags": {
			  "category": [
				"壽喜燒御膳"
			  ],
			  "kitchen": [
				"主廚"
			  ]
			},
			"behaviors": {},
			"visible": true,
			"globalize": {
			  "t": {
				"zh-Hant": {
				  "title": "和牛漢堡排御膳",
				  "brand": "高雄博愛店"
				}
			  },
			  "default": "zh-Hant"
			},
			"createdAt": "2025-03-20T05:09:11.000Z",
			"modifiedAt": "2025-05-01T09:25:30.000Z",
			"deletedAt": null,
			"id": "67dd71474f7760a5c8f60d24",
			"variationList": [],
			"bundleList": [
			  {
				"key": "addon-00",
				"title": "加購活動",
				"values": [
				  {
					"title": "起士牛肉炸水餃5顆",
					"sku": "uat-BI-add-on-1",
					"kind": "product",
					"price": 149,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/Frame1_150796cc-71e6-4d68-8518-9d42c698f718.jpg?v=1740392427"
					]
				  }
				],
				"required": false,
				"min": 0,
				"max": 1
			  },
			  {
				"key": "appetizer-00",
				"title": "先付",
				"value": [
				  "先付(溫泉蛋)"
				],
				"values": [
				  {
					"title": "先付(溫泉蛋)",
					"sku": "BI-AA11-uat",
					"kind": "product",
					"price": 0,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/75407e5864c54a8f22bc6367b12cd96f.png?v=1725375109"
					]
				  },
				  {
					"title": "先付(山藥泥)",
					"sku": "BI-AA12-uat",
					"kind": "product",
					"price": 0,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/0d235c732b3f0902f60be618f0b49b43.png?v=1725375196"
					]
				  }
				],
				"required": true,
				"min": 1,
				"max": 1
			  },
			  {
				"key": "rice-Z0",
				"title": "主食",
				"values": [
				  {
					"title": "松葉蟹肉丼",
					"sku": "BI-R01-uat",
					"kind": "product",
					"price": 140,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/78872438e6316cf129bf3d36b24d0a35.jpg?v=1744375564"
					]
				  },
				  {
					"title": "漬鮭魚卵丼",
					"sku": "BI-R02-uat",
					"kind": "product",
					"price": 140,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/b6099e7fc46cfbc1485f5f34d6df98f7_20aff185-04c1-415a-8911-c491a1668b3b.png?v=1723535945"
					]
				  },
				  {
					"title": "生拌和牛丼",
					"sku": "BI-R04-uat",
					"kind": "product",
					"price": 140,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/555fd22d15444169a53902cb37b33eaa.jpg?v=1741946233"
					]
				  },
				  {
					"title": "炙燒干貝丼",
					"sku": "BI-R03-uat",
					"kind": "product",
					"price": 140,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/Group307-4.png?v=1724216404"
					]
				  },
				  {
					"title": "鮭卵蟹膏丼",
					"sku": "BI-R05-uat",
					"kind": "product",
					"price": 140,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/Group308-4_0fc25640-eb0a-4328-83b7-b289bc1dcc15.png?v=1724191724"
					]
				  },
				  {
					"title": "海苔牛油白飯",
					"sku": "BI-AA04",
					"kind": "product",
					"price": 35,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/b2cd2ae267561410a559efe0e7ab1fbd_ca0845f2-35c6-4902-87c7-41b7c56ae026.png?v=1734332957"
					]
				  },
				  {
					"title": "越光米(可續)",
					"sku": "BI-D01",
					"kind": "product",
					"price": 0,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/11d5b0f64433db313b8ceac71c1c9adb.png?v=1721984674"
					]
				  }
				],
				"required": true,
				"min": 1,
				"max": 1
			  },
			  {
				"key": "dessert-00",
				"title": "甘味",
				"values": [
				  {
					"title": "哈根達斯冰淇淋(不挑口味)",
					"sku": "BI-H01",
					"kind": "product",
					"price": 60,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/fb86a850761ea8d752f4d4487c9840fb_177f7bc3-013e-42c4-9d02-713785076d3f.jpg?v=1740995602"
					]
				  },
				  {
					"title": "法式焦糖布蕾",
					"sku": "BI-AA07",
					"kind": "product",
					"price": 0,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/86f32c1640f359dae2ea808b41d98cf9.png?v=1733304614"
					]
				  },
				  {
					"title": "白玉紅豆紫米",
					"sku": "BI-AA08",
					"kind": "product",
					"price": 0,
					"options": [],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/3b72f6abc6a1a2a4ed7a3fe2cb60e030_ea2e0b05-59a4-423a-a516-ab59bf3b797f.png?v=1733304712"
					]
				  }
				],
				"required": true,
				"min": 1,
				"max": 1
			  },
			  {
				"key": "soup-00",
				"title": "飲品",
				"values": [
				  {
					"title": "蜂香奶綠",
					"sku": "BI-E09",
					"kind": "product",
					"price": 80,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/dca2b4bcc3da57e358d4fbf4b705bf97.png?v=1742697827"
					]
				  },
				  {
					"title": "桂花奶白",
					"sku": "BI-E08",
					"kind": "product",
					"price": 80,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/5c48102da34e0efcd3786f5503f9725d.png?v=1742697824"
					]
				  },
				  {
					"title": "檸檬紅茶",
					"sku": "BI-E11",
					"kind": "product",
					"price": 40,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/42aed85bbb388583299425ee57f3b532.png?v=1734332996"
					]
				  },
				  {
					"title": "純釀香綠茶",
					"sku": "BI-E01",
					"kind": "product",
					"price": 30,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/ef893e7e872e0332cad1baed647eca5c.png?v=1734332993"
					]
				  },
				  {
					"title": "桂花香紅茶",
					"sku": "BI-E02",
					"kind": "product",
					"price": 30,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/42aed85bbb388583299425ee57f3b532.png?v=1734332996"
					]
				  },
				  {
					"title": "可爾必思",
					"sku": "BI-E10",
					"kind": "product",
					"price": 30,
					"options": [
					  {
						"key": "ice",
						"title": "冰量",
						"values": [
						  {
							"id": "1",
							"title": "正常冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "2",
							"title": "少冰",
							"price": 0,
							"selected": false
						  },
						  {
							"id": "3",
							"title": "去冰",
							"price": 0,
							"selected": false
						  }
						],
						"required": true,
						"min": 1,
						"max": 1,
						"type": "tagList"
					  }
					],
					"images": [
					  "https://cdn.shopify.com/s/files/1/0639/8482/8572/files/f32469d3dc42b4d66d5a80d97c57d3b4_963a213f-d799-4a35-9b4a-0abc6e44991f.png?v=1724933767"
					]
				  }
				],
				"required": false,
				"min": 1,
				"max": 1
			  }
			]
		  }
	],
	VARIANTS = [
		{
		  "kind": "product",
		  "title": "和牛漢堡排御膳",
		  "position": 1,
		  "gtin": {
			"value": "BI-AR09"
		  },
		  "sku": "BI-AR09",
		  "inventory": {
			"management": "shopify",
			"policy": "continue",
			"lowQuantityWarningThreshold": 0,
			"quantity": -9
		  },
		  "channels": [],
		  "prices": [
			{
			  "name": "base",
			  "price": {
				"value": 438
			  },
			  "salePrice": {
				"value": 438
			  },
			  "countries": []
			}
		  ],
		  "taxable": true,
		  "variations": [
			{
			  "title": "Title",
			  "value": "Default Title"
			}
		  ],
		  "options": [
			{
			  "key": "sauce",
			  "title": "醬料",
			  "values": [
				{
				  "id": "12",
				  "title": "起司醬",
				  "price": 15,
				  "selected": false
				}
			  ],
			  "required": false,
			  "min": 0,
			  "max": 1,
			  "type": "radioboxList"
			}
		  ],
		  "minOrderQuantity": 1,
		  "fulfillmentService": "kitchen",
		  "preparation": {
			"time": 480
		  },
		  "external": {
			"shopify": {
			  "variantId": "42198247211094",
			  "inventoryItemId": "44312686198870",
			  "shop": "ichigyu-test.myshopify.com"
			}
		  },
		  "visible": true,
		  "globalize": {},
		  "createdAt": "2025-03-20T05:09:12.000Z",
		  "modifiedAt": "2025-05-01T09:21:07.000Z",
		  "deletedAt": null,
		  "id": "67dd714a4f7760a5c8f60e19",
		  "productId": "67dd71474f7760a5c8f60d24",
		  "imageIds": [
			"681259f1591699003b87931e"
		  ],
		  "images": [
			{
				"original" : {
					"url" : "https://cdn.shopify.com/s/files/1/0596/4313/4038/files/4cac3a6f4035201fa53545d45d096ee8.png?v=1742447271"
				}
			}
		  ]
		}
	  ]
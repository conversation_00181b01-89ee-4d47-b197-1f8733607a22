import { createHmac } from 'node:crypto'
import { Api<PERSON>emote, EVE<PERSON>, Headers, MediaType, OAuth } from '@perkd/api-request'
import { Credentials, Config, SERVICE } from './types'

const ENDPOINTS = require('./Uber-endpoints.json'),
	{ AUTHORIZATION, CONTENT_TYPE } = Headers,
	{ FORM_URLENCODED, JSON_DATA } = MediaType,
	{ CLIENT_CREDENTIALS } = OAuth.GrantType,
	{ AUTH } = SERVICE,
	TEST = 'test'

export class UberRemote extends ApiRemote {

	private credentials: Credentials

	constructor(credentials: Credentials, settings: Config = {}) {
		const { clientId, secret, scope, merchantId } = credentials,
			{ axios = {}, ...rest } = settings

		if (!clientId || !secret || !scope || !merchantId) throw 'Incomplete credentials'

		axios.withCredentials = true
		axios.headers = { [CONTENT_TYPE]: JSON_DATA }

		super({ ...rest, axios })
		this.credentials = credentials

		this.remote.client.interceptors.request.use(
			(config: any) => {
				const { accessToken } = this

				if (accessToken) {
					config.headers[AUTHORIZATION] = `Bearer ${accessToken}`
				}
				return config
			}
		)
		this.remote.on(EVENT.unauthorized, ({ retryCount, lastRequestTime, url }: any) => {
			if (url !== this.url(AUTH)) {
				this.refreshToken()
			}
		})
	}

	get accessToken() {
		return this.credentials.accessToken
	}

	protected get secret() {
		return this.credentials.secret
	}

	protected get clientId() {
		return this.credentials.clientId
	}

	protected get scope() {
		return this.credentials.scope
	}

	async refreshToken(): Promise<void> {
		try {
			const { clientId: client_id, secret: client_secret, scope } = this,
				url = this.url(AUTH),
				headers = { [ CONTENT_TYPE ]: FORM_URLENCODED },
				grant_type = CLIENT_CREDENTIALS,
				body = { client_id, client_secret, grant_type, scope },
				credentials = await this.post(url, body, { headers }),
				{ access_token, expires_in } = credentials ?? {}

			if (access_token) {
				this.credentials.accessToken = access_token
				this.credentials.expiresAt = new Date(Date.now() + expires_in * 1000)
			}
		}
		catch (err: any) {
			const { context = {} } = err,
				{ message, data } = context

			console.error(message, data)
		}
	}

	signature(content: string): string {
		const { secret } = this,
			signature = createHmac('sha256', secret)
				.update(content)
				.digest('hex')

		return signature
	}

	protected endpoint(service: string) {
		const { env = TEST, scope } = this,
			[ foodOrMart ] = scope.split('.')

		return ENDPOINTS[foodOrMart][env][service]
	}
}

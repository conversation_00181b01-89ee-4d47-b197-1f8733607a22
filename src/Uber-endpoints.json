{"eats": {"test": {"auth": {"url": "https://auth.uber.com/oauth/v2/token", "port": 80}, "store": {"url": "https://api.uber.com/v1/delivery/store/{storeId}", "port": 80}, "menu": {"url": "https://api.uber.com/v2/eats/stores/{storeId}/menus", "port": 80}, "order": {"url": "https://api.uber.com/v1/delivery/order/{orderId}", "port": 80}}, "production": {"auth": {"url": "https://auth.uber.com/oauth/v2/token", "port": 80}, "store": {"url": "https://api.uber.com/v1/delivery/store/{storeId}", "port": 80}, "menu": {"url": "https://api.uber.com/v2/eats/stores/{storeId}/menus", "port": 80}, "order": {"url": "https://api.uber.com/v1/delivery/order/{orderId}", "port": 80}}}}
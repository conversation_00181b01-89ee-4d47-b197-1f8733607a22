import { Base, Provider, FulfillmentsInterface } from '@provider/providers'
import { UberSDK } from './sdk'

type Id = string
type Fulfillment = any

export class Fulfillments extends Base implements FulfillmentsInterface {

	declare readonly API: UberSDK

	constructor(provider: Provider) {
		super(provider)
	}

	async ready(orderId: string, status?: number) {
		await this.API.orderReady(orderId, status)
	}

	// async collected(orderId: string) {
	// 	await this.API.orderCollected(orderId)
	// }

	// async delivered(orderId: string) {
	// 	await this.API.orderDelivered(orderId)
	// }

	async create(params: any, orderId?: Id): Promise<Fulfillment> {
	}

	async complete(fulfillmentId: Id, orderId?: Id): Promise<void> {
	}

	async cancel(fulfillmentId: Id, orderId: Id): Promise<void> {
	}

	async list(orderId: Id): Promise<Fulfillment[]> {
		return []
	}
}

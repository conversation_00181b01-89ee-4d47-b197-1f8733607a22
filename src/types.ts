/**
 * Ref:  https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
 */

import { RemoteConfig } from '@perkd/api-request'
import { Products, Contacts, Places } from '@crm/types'

// ---  CRM

export type Address = Contacts.Address
export type Phone = Contacts.Phone

export type Product = Products.Product
export type Variant = Products.Variant & { images: Products.Image[] }
export type Place = Places.Place

// ---  Uber

export enum Scope {
	STORE = 'eats.store',
	ORDER = 'eats.order'
}

export type Credentials = {
	clientId: string
	secret: string
	merchantId: string
	scope: Scope
	accessToken?: string
	expiresAt?: Date
}

export type Config = RemoteConfig & {
}

// Service endpoints
export const SERVICE = {
	AUTH: 'auth',
	STORE: 'store',
	MENU: 'menu',
	ORDER: 'order',
}

/**
 * Comprehensive denial reasons for UberEats orders
 * Based on UberEats API documentation and best practices
 */
export enum DenialReason {
	/** Restaurant is too busy to fulfill orders */
	RESTAURANT_TOO_BUSY = 'RESTAURANT_TOO_BUSY',
	/** Restaurant kitchen is closed */
	KITCHEN_CLOSED = 'KITCHEN_CLOSED',
	/** Specific menu items are unavailable */
	ITEM_UNAVAILABLE = 'ITEM_UNAVAILABLE',
	/** Items are out of stock */
	OUT_OF_STOCK = 'OUT_OF_STOCK',
	/** Technical issues with POS integration */
	POS_INTEGRATION_ERROR = 'POS_INTEGRATION_ERROR',
	/** System technical issues */
	SYSTEM_ERROR = 'SYSTEM_ERROR',
	/** Preparation time exceeds delivery window */
	PREPARATION_TIME_TOO_LONG = 'PREPARATION_TIME_TOO_LONG',
	/** Order received after restaurant cutoff time */
	PAST_CUTOFF_TIME = 'PAST_CUTOFF_TIME',
	/** Restaurant at full capacity */
	FULL_CAPACITY = 'FULL_CAPACITY',
	/** Invalid or problematic order */
	INVALID_ORDER = 'INVALID_ORDER',
	/** Address delivery issues */
	DELIVERY_ADDRESS_ISSUE = 'DELIVERY_ADDRESS_ISSUE',
	/** General inability to fulfill */
	UNABLE_TO_FULFILL = 'UNABLE_TO_FULFILL'
}

/**
 * Cancel reasons aligned with denial reasons for consistency
 */
export enum CancelReason {
	RESTAURANT_TOO_BUSY = 'RESTAURANT_TOO_BUSY',
	KITCHEN_CLOSED = 'KITCHEN_CLOSED', 
	ITEM_UNAVAILABLE = 'ITEM_UNAVAILABLE',
	OUT_OF_STOCK = 'OUT_OF_STOCK',
	SYSTEM_ERROR = 'SYSTEM_ERROR',
	PREPARATION_TIME_TOO_LONG = 'PREPARATION_TIME_TOO_LONG',
	FULL_CAPACITY = 'FULL_CAPACITY',
	INVALID_ORDER = 'INVALID_ORDER',
	DELIVERY_ADDRESS_ISSUE = 'DELIVERY_ADDRESS_ISSUE',
	UNABLE_TO_FULFILL = 'UNABLE_TO_FULFILL',
	/** Customer requested cancellation */
	CUSTOMER_CANCELLED = 'CUSTOMER_CALLED_TO_CANCEL',
	/** Other unspecified reason */
	OTHER = 'OTHER'
}

/**
 * Options for enhanced order rejection
 */
export interface RejectOptions {
	/** Reason for denial */
	reason?: DenialReason
	/** Additional details/context */
	details?: string
	/** Whether to force reject (deny + cancel) */
	forceReject?: boolean
	/** Custom cancel code if force rejecting */
	cancelCode?: number
}

/**
 * Mapping denial reasons to appropriate cancel codes
 */
export const DENIAL_TO_CANCEL_CODE: Record<DenialReason, number> = {
	[DenialReason.RESTAURANT_TOO_BUSY]: 1001,
	[DenialReason.KITCHEN_CLOSED]: 1002,
	[DenialReason.ITEM_UNAVAILABLE]: 1003,
	[DenialReason.OUT_OF_STOCK]: 1004,
	[DenialReason.POS_INTEGRATION_ERROR]: 1005,
	[DenialReason.SYSTEM_ERROR]: 1006,
	[DenialReason.PREPARATION_TIME_TOO_LONG]: 1007,
	[DenialReason.PAST_CUTOFF_TIME]: 1008,
	[DenialReason.FULL_CAPACITY]: 1009,
	[DenialReason.INVALID_ORDER]: 1010,
	[DenialReason.DELIVERY_ADDRESS_ISSUE]: 1011,
	[DenialReason.UNABLE_TO_FULFILL]: 1012
}

/**
 * Mapping denial reasons to cancel reasons for consistency
 */
export const DENIAL_TO_CANCEL_REASON: Record<DenialReason, CancelReason> = {
	[DenialReason.RESTAURANT_TOO_BUSY]: CancelReason.RESTAURANT_TOO_BUSY,
	[DenialReason.KITCHEN_CLOSED]: CancelReason.KITCHEN_CLOSED,
	[DenialReason.ITEM_UNAVAILABLE]: CancelReason.ITEM_UNAVAILABLE,
	[DenialReason.OUT_OF_STOCK]: CancelReason.OUT_OF_STOCK,
	[DenialReason.POS_INTEGRATION_ERROR]: CancelReason.SYSTEM_ERROR,
	[DenialReason.SYSTEM_ERROR]: CancelReason.SYSTEM_ERROR,
	[DenialReason.PREPARATION_TIME_TOO_LONG]: CancelReason.PREPARATION_TIME_TOO_LONG,
	[DenialReason.PAST_CUTOFF_TIME]: CancelReason.RESTAURANT_TOO_BUSY,
	[DenialReason.FULL_CAPACITY]: CancelReason.FULL_CAPACITY,
	[DenialReason.INVALID_ORDER]: CancelReason.INVALID_ORDER,
	[DenialReason.DELIVERY_ADDRESS_ISSUE]: CancelReason.DELIVERY_ADDRESS_ISSUE,
	[DenialReason.UNABLE_TO_FULFILL]: CancelReason.UNABLE_TO_FULFILL
}

export enum EntityType {
	ITEM = 'ITEM',
	MODIFIER_GROUP = 'MODIFIER_GROUP'
}

export enum ContextType {
	MENU = 'MENU',
	ITEM = 'ITEM',
	MODIFIER_GROUP = 'MODIFIER_GROUP'
}

export enum DisplayType {
	EXPANDED = 'expanded',
	COLLAPSED = 'collapsed'
}


export enum OrderState {
	CREATED = 'CREATED',
	OFFERED = 'OFFERED',
	ACCEPTED = 'ACCEPTED',
	HANDED_OFF = 'HANDED_OFF',
	SUCCEEDED = 'SUCCEEDED',
	FAILED = 'FAILED',
	UNKNOWN = 'UNKNOWN'
}

export enum OrderStatus {
	SCHEDULED = 'SCHEDULED',
	ACTIVE = 'ACTIVE',
	COMPLETED = 'COMPLETED'
}

export enum PrepStatus {
	PREPARING = 'PREPARING',
	OUT_OF_ITEM = 'OUT_OF_ITEM_PENDING_CUSTOMER_RESPONSE',
	READY = 'READY_FOR_HANDOFF'
}

export enum DeliveryStatus {
	SCHEDULED = 'SCHEDULED'
}

export enum FulfillmentType {
	UBER_DELIVER = 'DELIVERY_BY_UBER',
	MERCHANT_DELIVER = 'DELIVERY_BY_MERCHANT',
	PICK_UP = 'PICKUP',
	DINE_IN = 'DINE_IN',
}

export enum InteractionType {
	TO_DOOR = 'DELIVER_TO_DOOR',
	CURBSIDE = 'CURBSIDE',
}

export enum PriceType {
	ITEM = 'ITEM'
}

export enum CancelType {
	KITCHEN_CLOSED = 'KITCHEN_CLOSED',
	KITCHEN_BUSY = 'RESTAURANT_TOO_BUSY',
	INVALID_ITEM = 'ITEM_ISSUE',
	INVALID_ORDER = 'ORDER_VALIDATION',
	INVALID_ADDRESS = 'ADDRESS',
	FULL_CAPACITY = 'CAPACITY',
	OTHER = 'OTHER',
	CUSTOMER_REQUEST = 'CUSTOMER_CALLED_TO_CANCEL'
}

export type MultiLanguageText = {
	translations: {
		[language: string]: string
	}
}

export type Store = {
	id: string
	name: string
	timezone: string
	partner_identifiers: {
		value: string		// eg. "REDACTED"
		type: string		// eg. "ORDER_MANAGER_CLIENT_ID"
	}[]
}

type Contact = {
	phone: {
		country_iso2: string	// "US"
		number: string			// "+886 2 5594 1277"
		pin_code: string		// "470 25 789"
	}
}

type TaxProfile = {
	tax_id: string
	tax_id_type: string			// twn-vat
	profile_type: string		// PERSONAL
	country: string				// TW
}

export type Customer = {
	id: string
	name: {
		display_name: string
		first_name: string
		last_name: string
	}
	contact: Contact
	tax_profiles: TaxProfile[]
	order_history: {
		past_order_count: number
	}
	is_primary_customer: boolean
	can_respond_to_fulfillment_issues: boolean
}

type Quantity = {
	amount: number
	unit: string
}

export type MenuEntity = {
	id?: string					// of item or modifier group
	type?: EntityType
}

// ---  ModifierGroup
//	https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu#request-body-parameters-modifiergroup


type QuantityConstraint = {
	min_permitted?: number		// min quantity allowed (inclusive)
	max_permitted?: number		// max quantity allowed (inclusive), >= min_permitted
	is_min_permitted_optional?: boolean		// default FALSE
	default_quantity?: number
	charge_above?: number		// item price will only be charged per quantity unit in excess of this amount
	refund_under?: number		// item price will be refunded per quantity unit chosen below this amount
	min_permitted_unique?: number	// min quantity of unique customization selections allowed 
	max_permitted_unique?: number	// max quantity of unique customization selections allowed 
}

type QuantityConstraintOverride = {
	context_type: ContextType
	context_value: string		// identifying string (id) for the specified context
	quantity: QuantityConstraint
}

// constrains quantities in which all items within the modifier group can be ordered
type QuantityConstraintRules = {
	quantity: QuantityConstraint
	overrides?: QuantityConstraintOverride[]
}

export type ModifierGroup = {
	id: string					// unique id provided by merchant
	title: MultiLanguageText
	external_data: string		// metadata
	modifier_options: MenuEntity[]
	quantity_info?: QuantityConstraintRules
	display_type?: DisplayType
}

// ---  Order

enum MeasurementType {
	COUNT = 'MEASUREMENT_TYPE_COUNT',
	WEIGHT = 'MEASUREMENT_TYPE_WEIGHT',
	VOLUME = 'MEASUREMENT_TYPE_VOLUME',
	LENGTH = 'MEASUREMENT_TYPE_LENGTH',
	INVALID = 'MEASUREMENT_TYPE_INVALID',
}

enum LengthUnit {
	METER = 'LENGTH_UNIT_TYPE_METRIC_METER',
	CENTIMETER = 'LENGTH_UNIT_TYPE_METRIC_CENTIMETER',
	MILLIMETER = 'LENGTH_UNIT_TYPE_METRIC_MILLIMETER',
}

enum WeightUnit {
	KILOGRAM = 'WEIGHT_UNIT_TYPE_METRIC_KILOGRAM',
	GRAM = 'WEIGHT_UNIT_TYPE_METRIC_GRAM',
	MILLIGRAM = 'WEIGHT_UNIT_TYPE_METRIC_MILLIGRAM',
	POUND = 'WEIGHT_UNIT_TYPE_IMPERIAL_POUND',
	OUNCE = 'WEIGHT_UNIT_TYPE_IMPERIAL_OUNCE',
}

enum VolumeUnit {
	LITER = 'VOLUME_UNIT_TYPE_METRIC_LITER',
	MILLILITER = 'VOLUME_UNIT_TYPE_METRIC_MILLILITER',
	OUNCE = 'VOLUME_UNIT_TYPE_US_FLUID_OUNCE',
}

type MeasurementUnit = {
	measurement_type: MeasurementType
	length_unit?: LengthUnit
	weight_unit?: WeightUnit
	volume_unit?: VolumeUnit
}

type TaxInfo = {
	tax_rate?: number				// float, to be charged on top of the provided menu item price
	vat_rate_percentage?: number	// float, value-added tax rate for the item. This is the amount of tax already included in the menu item's price
}

type PriceOverride = {
	context_type: ContextType
	context_value: string			// id for the specified context
	price: number					// price of the item in the percent of local currency denomination
	core_price?: number				// intrinsic value of the item, in the percent of local currency denomination
}

type PriceRules = {
	price: number					// price of the item in the percent of local currency denomination
	core_price?: number				// intrinsic value of the item, in the percent of local currency denomination
	container_deposit?: number
	overrides?: PriceOverride[]
	priced_by_unit?: MeasurementUnit	// "per measurement" unit the item price is based on
}

type ModifierGroupsOverride = {
	context_type: ContextType
	context_value: string			// id for the specified context
	ids: string[]					// list of the ids of all modifier groups associated with the item in this context
}

type ModifierGroupsRules = {
	ids: string[]
	overrides?: ModifierGroupsOverride[]
}

type ProductInfo = {
	gtin?: string					// UPC/EAN
	plu?: string					// for fresh produce, https://www.ifpsglobal.com/PLU-Codes
	merchant_id?: string			// merchant's internal id, eg. sku
	product_type?: string
	product_traits?: string[]
	countries_of_origin?: string[]
	target_market?: number			// ISO 3166 Numeric code, 1 for Global, 97 for EU, https://en.wikipedia.org/wiki/ISO_3166-1
}

// how an item can be sold by
type SellingOption = {
	sold_by_unit?: MeasurementType
	// quantity_constraints?: SellingQuantityConstraint
	// priced_by_to_sold_by_unit_conversion_info?: PricedByToSoldByUnitConversionInfo
}

type SellingInfo = {
	selling_options: SellingOption[]
}

export type CartItem = {
	id: string
	cart_item_id: string
	customer_id: string
	title: string
	external_data: string
	quantity: Quantity
	selected_modifier_groups?: ModifierGroup[]
	picture_url?: string,
	customer_request?: {
		special_instructions?: string
	}
}

type Cart = {
	id: string
	revision_id: string
	items: CartItem[]
	include_single_use_items: boolean
	special_instructions: string
	restricted_items: {
		alcohol: {
			contain_alcoholic_item: boolean
		}
	}
}

type Location = {
	id: string
	type: string
	street_address_line_one: string
	street_address_line_two: string
	unit_number: string
	business_name?: string
	latitude: string
	longitude: string
	city: string
	country: string
	postal_code: string
	location_type_value: string
	client_provided_street_address_line_one: string
}

type Delivery = {
	id: string
	status: DeliveryStatus
	location?: Location
    interaction_type: InteractionType
    instructions: string
    estimated_pick_up_time: Date
    estimated_drop_off_time: Date
}

export type Amount = {
	amount_e5: number		// x100000
	currency_code: string
	formatted: string
}

type Total = {
	gross: Amount
	display_amount: string
	net?: Amount
	tax?: Amount
	is_tax_inclusive?: boolean
}

type Price = {
	cart_item_id: string
	price_type: PriceType
	quantity: Quantity
	total: Total
	unit: Total
}

type Eligible = {
	is_eligible: boolean
	reason: string
}

export type Payment = {
	payment_detail: {
		order_total: Total
		item_charges: {
			total: Total
			price_breakdown: Price[]
		}
		fees: {
			total: Total
		}
		promotions: {
			total: Total
			order_total_excluding_promos: Total
		}
		cash_amount_due?: {
			gross: Total['gross']
		}
		currency_code: string		// ISO 4217
		marketplace_fee_due_to_uber: Total
	}
	tax_reporting: {
		breakdown: {
			items: any[]
			fees: any[]
			promotions: any[]
		}
	}
	tender_types: string | null
}

export type Order = {
	id: string
	display_id: string
	state: OrderState
	status: OrderStatus
	store: Store
	customers: Customer[]
	carts: Cart[]
	deliveries: Delivery[]
	payment: Payment
	fulfillment_type: FulfillmentType
	store_instructions?: string		// from customer to store
	created_time: Date
	preparation_time: {
		source: string				// "DEFAULT"
		ready_for_pickup_time_secs: number
		ready_for_pickup_time: Date
	}
	preparation_status: PrepStatus
	action_eligibility: {
		adjust_ready_for_pickup_time: Eligible
		mark_out_of_item: Eligible
		cancel: Eligible
		mark_cannot_fulfill: Eligible
	}
	storeId?: string				// injected by caller
	external?: any					// injected

	// ordering_platform: string
	// is_order_accuracy_risk: false
	// estimated_unfulfilled_at: "2024-03-12T19:34:37+08:00"
	// has_membership_pass: false
	// created_time: "2024-03-12T19:23:07+08:00"
}

export type AppOrderOptions = {
	createdAt?: Date
	sendReceipt?: boolean
}

// ---  Menu
//	https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu#request-body-parameters-menuconfiguration

export enum MenuType {
	DELIVER = 'MENU_TYPE_FULFILLMENT_DELIVERY',
	PICKUP = 'MENU_TYPE_FULFILLMENT_PICK_UP',
	DINEIN = 'MENU_TYPE_FULFILLMENT_DINE_IN'
}

export enum DayOfWeek {
	MONDAY = 'monday',
	TUESDAY = 'tuesday',
	WEDNESDAY = 'wednesday',
	THURSDAY = 'thursday',
	FRIDAY = 'friday',
	SATURDAY = 'saturday',
	SUNDAY = 'sunday',
}

export type TimePeriod = {
	start_time: string			// 24-hour - HH:MM
	end_time: string
}

export type ServiceAvailability = {
	day_of_week: DayOfWeek
	time_periods: TimePeriod[]
}

export type Menu = {
	id: string					// unique identifier (provided by merchant)
	title: MultiLanguageText
	subtitle?: MultiLanguageText
	service_availability: ServiceAvailability[]
	category_ids: string[]		// all ids of menu categories
	invisible: boolean
}

export type Category = {
	id: string
	title: MultiLanguageText
	subtitle?: MultiLanguageText
	entities: MenuEntity[]
}

export type MenuItem = {
	id: string						// unique id provided by merchant
	title: MultiLanguageText
	tax_info: TaxInfo
	description?: MultiLanguageText
	image_url?: string				// JPG, PNG, WEBP, 320px ≤ Width ≤ 6000px, 320px ≤ Height ≤ 6000px
	external_data?: string			// metadata
	price_info: PriceRules
	quantity_info?: QuantityConstraintRules
	modifier_group_ids?: ModifierGroupsRules
	// suspension_info?: SuspensionRules
	product_info?: ProductInfo		// product identification information i.e. GTIN/UPC codes
	selling_info?: SellingInfo
	// nutritional_info?: NutritionalInfo
	// dish_info?: DishInfo
	// visibility_info?: VisibilityInfo
	// tax_label_info?: TaxLabelsRuleSet
	// bundled_items?: BundledItems[]
	// beverage_info?: BeverageInfo
	// physical_properities_info?: PhysicalPropertiesInfo
	// medication_info?: PhysicalPropertiesInfo
}

export type MenuConfiguration = {
	menus: Menu[]
	categories: Category[]
	items: MenuItem[]
	modifier_groups: ModifierGroup[]
}

export const EMPTY_MENU: any = {
	menus: [{
		id: 'replace',
		title: {
			translations: { en_us: 'Empty Menu' }
		},
		service_availability: [{
			day_of_week: 'monday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'tuesday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'wednesday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'thursday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'friday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'saturday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}, {
			day_of_week: 'sunday',
			time_periods: [{ start_time: '00:00', end_time: '23:59' }]
		}],
		category_ids: []
	}],
	items: [],
	categories: [],
	modifier_groups: [],
	display_options: {}
}

export type ListOptions = {
	day?: Date
	next?: string
}

export type OrderList = {
	orders: Order[]
	next?: string			// next page token
}

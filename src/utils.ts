import { Currencies } from '@perkd/utils'
import { Amount } from './types'

type Country = 'TW' | 'HK'

const { nativeAmount } = Currencies,
	ONE_DAY = 24 * 60 * 60 * 1000,		// milliseconds
	ONE_YEAR = ONE_DAY * 365,
	NOW = new Date().getTime(),
	defaultStart = new Date(NOW - ONE_YEAR),
	defaultEnd = new Date(NOW + ONE_YEAR)

defaultStart.setUTCHours(16, 0, 0, 0)
defaultEnd.setUTCHours(15, 59, 59, 0)

export function amount2Native(amount?: Amount): number {
	if (!amount) return 0

	const { amount_e5, currency_code } = amount,
		price = amount_e5 / 100000

	return nativeAmount(price, currency_code)
}
	
export function taxRate(tax_reporting: any = {}): number {
	const { breakdown } = tax_reporting,
		[item] = breakdown?.items ?? {},
		[tax] = item?.taxes ?? {}

	return tax.rate ?? 0
}

export function cleanse(text?: string): string {
	if (!text) return ''

	const regexPatterns: RegExp[] = [ /\| GUI\/TIN: \d+$/ ]

	for (const pattern of regexPatterns) {
		text = text.replace(pattern, "");
	}

	return text.trim()
}

// ----  Private functions

/**
 * Convert duration to timestamp RFC3339 format
 * @param duration - '30m', '1h' or '24h'
 */
export function duration2Until(duration: string = '1h'): string {
	const minutes = duration === '30m'
		? 30
		: duration === '1h' ? 60 : 24 * 60

	return new Date(Date.now() + minutes * 60000).toISOString()
}

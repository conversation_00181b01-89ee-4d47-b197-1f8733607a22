import { Base, Provider } from '@provider/providers'
import { UberSDK } from './sdk'

const DURATION = ['30m', '1h', '24h']

export class Stores extends Base {

	declare readonly API: UberSDK
	readonly merchantId: string

	constructor(provider: Provider, merchantId: string) {
		super(provider)
		this.merchantId = merchantId
	}

	/**
	 * @param merchantId 
	 * @param duration - 1 = 30m, 2 = 1h, 3 = 24h
	 */
	async pause(merchantId: string = this.merchantId, duration?: number) {
		const durationStr = duration ? DURATION[duration-1] || undefined : undefined
		await this.API.pauseStore(merchantId, true, durationStr).catch(() => null)
	}

	async resume(merchantId: string = this.merchantId) {
		await this.API.pauseStore(merchantId, false).catch(() => null)
	}
}

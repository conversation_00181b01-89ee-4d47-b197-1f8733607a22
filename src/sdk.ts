/**
 *  @module SDK class for Uber (Eats)
 */
import { gzip, Headers } from '@perkd/api-request'
import { UberRemote } from './uber-remote'
import { Credentials, Config, SERVICE, MenuConfiguration, MenuType, EMPTY_MENU, DenialReason, CancelReason, DENIAL_TO_CANCEL_CODE, DENIAL_TO_CANCEL_REASON } from './types'
import { duration2Until } from './utils'

const { CONTENT_ENCODING } = Headers,
	{ STORE, ORDER, MENU } = SERVICE,
	SIGNATURE_HEADER = 'x-uber-signature',	// converted to lowercase by AWS lambda, original: X-Uber-Signature
	// merchant methods
	STORE_STATUS = 'update-store-status',
	// store status
	ONLINE = 'ONLINE',
	OFFLINE = 'OFFLINE',
	// order methods
	ACCEPT = 'accept',
	DENY = 'deny',
	READY = 'ready',	// ready for pickup
	DELIVERY = 'delivery',
	CANCEL = 'cancel'

export class UberSDK {

	readonly api: UberRemote

	constructor(credentials: Credentials, options?: Config) {
		this.api = new UberRemote(credentials, options)
	}

	// --- Store ---

	/**
	 * Temporarily pause store
	 *
	 *	ref:	https://developer.uber.com/docs/eats/references/api/store_suite#tag/SetStoreStatus
	 * @param storeId 
	 * @param isPause - false => resume
	 * @param [duration] - '30m', '1h' or '24h' (when isPause = true)
	 */
	async pauseStore(storeId: string, isPause: boolean = true, duration?: string): Promise<void> {
		try {
			const { api } = this,
				url = api.url(STORE, STORE_STATUS, { storeId }),
				status = isPause ? OFFLINE : ONLINE,
				is_offline_until = isPause ? duration2Until(duration) : undefined,
				reason = 'full'

			await api.post(url, { status, reason, is_offline_until })
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * Get Menu
	 * 		https://developer.uber.com/docs/eats/references/api/v2/get-eats-stores-storeid-menu
	 * @param storeId 
	 * @param type of menu 
	 */
	async getMenu(storeId: string, type: MenuType = MenuType.DELIVER): Promise<MenuConfiguration> {
		try {
			const { api } = this,
				url = api.url(MENU, '?menu_type={type}', { storeId, type })

			return api.get(url)
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * Upsert Menu
	 * 		https://developer.uber.com/docs/eats/references/api/v2/put-eats-stores-storeid-menu
	 * @param storeId 
	 * @param menu 
	 */
	async updateMenu(storeId: string, menu: MenuConfiguration): Promise<void> {
		try {
			const { api } = this,
				url = api.url(MENU, undefined, { storeId }),
				body = await gzip(menu),
				headers = { [CONTENT_ENCODING]: 'gzip' }

			await api.put(url, body, { headers })
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * Remove Menu
	 * @param storeId 
	 * @param menuId
	 */
	async removeMenu(storeId: string, menuId: string): Promise<void> {
		try {
			const { api } = this,
				url = api.url(MENU, undefined, { storeId })

			EMPTY_MENU.menus[0].id = menuId
			await api.put(url, EMPTY_MENU)
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	// --- Order ---

	/**
	 * Accept order
	 * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/AcceptOrder/operation/acceptOrder
	 * @param orderId
	 * @param accept
	 * @param readyAt
	 * @param external_id
	 * @param denialReason - Custom denial reason when rejecting
	 */
	async acceptOrder(
		orderId: string, 
		accept: boolean = true, 
		readyAt?: Date, 
		external_id?: string,
		denialReason?: DenialReason,
		denialDetails?: string
	): Promise<void> {
		try {
			const { api } = this,
				state = accept ? ACCEPT : DENY,
				ready_for_pickup_time = accept
					? readyAt?.toISOString()
					: undefined,
				deny_reason = accept
					? undefined
					: { 
						type: denialReason || DenialReason.RESTAURANT_TOO_BUSY, 
						info: denialDetails || 'Unable to fulfill order'
					},
				url = api.url(ORDER, state, { orderId })

			await api.post(url, { ready_for_pickup_time, deny_reason, external_id })
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * Cancel order with enhanced reason support
	 * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/CancelOrder
	 * @param orderId
	 * @param cancelReason - Reason for cancellation
	 * @param cancelCode - Custom error code
	 */
	async cancelOrder(
		orderId: string, 
		cancelReason: CancelReason = CancelReason.RESTAURANT_TOO_BUSY,
		cancelCode?: number
	): Promise<void> {
		try {
			const { api } = this,
				url = api.url(ORDER, CANCEL, { orderId }),
				type = cancelReason,
				client_error_code = cancelCode || 1001

			await api.post(url, { type, client_error_code })
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * Get active Order
	 * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
	 * @param orderId
	 */
	async getOrder(orderId: string): Promise<any> {
		try {
			const { api } = this,
				url = api.url(ORDER, '?expand=carts,deliveries,payment', { orderId }),
				{ order } = await api.get(url) ?? {}

			return order
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	/**
	 * List of orders for day
	 * 		https://developer.uber.com/docs/eats/references/api/order_suite#tag/GetOrders
	 * @param storeId
	 * @param day - not supported for now
	 * @param next_page_token
	 */
	async listOrders(storeId: string, day: Date = new Date(), next_page_token?: string): Promise<any> {
		try {
			const { api } = this,
				url = api.url(STORE, 'orders', { storeId, next_page_token }),
				res = await api.get(url) ?? {}

			return res
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	// --- Fulfillment ---

	/**
	 * Mark order as Ready for delivery or completed for dine-in
	 * 	ref:	https://developer.uber.com/docs/eats/references/api/order_suite#tag/OrderReady/operation/orderReady
	 * @param orderId 
	 * @param markStatus 1 = ready (for delivery), 2 = completed (dine-in)
	 */
	async orderReady(orderId: string, markStatus: number = 1): Promise<void> {
		try {
			const { api } = this,
				url = api.url(ORDER, READY, { orderId })

			await api.post(url)
		}
		catch (err) {
			throw this.handleError(err)
		}
	}

	verifyWebhook(headers: any, body: string | any) {
		const { api } = this,
			signature = headers[SIGNATURE_HEADER] || '',
			content = (typeof body === 'string') ? body : JSON.stringify(body),
			hmac = api.signature(content)

		return signature === hmac
	}

	private handleError(res: any) {
		const { context, stack } = res,
			{ response, message } = context || res,
			{ status, code } = response || res

		return { code: code || status, message }
	}
}

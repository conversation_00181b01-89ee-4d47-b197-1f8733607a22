{"id": "a2b009de-0e0e-41ad-b811-ce797b0ac962", "display_id": "AC962", "state": "ACCEPTED", "ordering_platform": "UBER_EATS", "store": {"id": "f0a3f47b-b306-4134-8a04-8c26b7e2184e", "name": "Perkd Test Store", "timezone": "Asia/Taipei", "partner_identifiers": [{"value": "REDACTED", "type": "ORDER_MANAGER_CLIENT_ID"}]}, "customers": [{"id": "e552755d-84bf-522c-8ee3-816bc08391ba", "name": {"display_name": "Perkd T.", "first_name": "Perkd", "last_name": "T."}, "order_history": {"past_order_count": 0}, "is_primary_customer": true, "contact": {"phone": {"country_iso2": "US", "number": "+886 2 5594 1277", "pin_code": "024 18 597"}}, "can_respond_to_fulfillment_issues": false}], "deliveries": [{"id": "592532c2-93cd-4ef5-8bea-736df982de07", "status": "SCHEDULED", "estimated_pick_up_time": "2024-03-12T21:04:07+08:00", "interaction_type": "DELIVER_TO_DOOR", "estimated_drop_off_time": "2024-03-12T21:05:00+08:00"}], "carts": [{"id": "ab1a9ff4-8b38-41ea-aa08-5f6a34a3ee05", "revision_id": "115f167a-fa15-5075-a85e-b30261bffd48", "items": [{"id": "Chicken-sandwich", "cart_item_id": "ff1f7347-b239-4bf2-9170-fdf8ed1abe63", "customer_id": "e552755d-84bf-522c-8ee3-816bc08391ba", "external_data": "External data for chicken sandwich", "title": "Chicken sandwich", "quantity": {"amount": 1, "unit": "PIECE"}}], "include_single_use_items": false, "restricted_items": {"alcohol": {"contain_alcoholic_item": false}}}], "payment": {"payment_detail": {"order_total": {"net": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "display_amount": "NT$7.00", "is_tax_inclusive": true}, "item_charges": {"total": {"net": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "display_amount": "NT$7.00", "is_tax_inclusive": true}, "price_breakdown": [{"cart_item_id": "ff1f7347-b239-4bf2-9170-fdf8ed1abe63", "price_type": "ITEM", "quantity": {"amount": 1, "unit": "PIECE"}, "total": {"gross": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "display_amount": "NT$7.00"}, "unit": {"gross": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "display_amount": "NT$7.00"}}]}, "fees": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}}, "promotions": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}, "order_total_excluding_promos": {"net": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "display_amount": "NT$7.00", "is_tax_inclusive": true}}, "currency_code": "TWD", "marketplace_fee_due_to_uber": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "NT$0.00"}, "display_amount": "NT$0.00", "is_tax_inclusive": true}}, "tax_reporting": {"breakdown": {"items": [{"instance_id": "ff1f7347-b239-4bf2-9170-fdf8ed1abe63", "description": "ITEM", "gross_amount": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "net_amount": {"amount_e5": 666666, "currency_code": "TWD", "formatted": "NT$6.67"}, "total_tax": {"amount_e5": 33333, "currency_code": "TWD", "formatted": "NT$0.33"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 33333, "currency_code": "TWD", "formatted": "NT$0.33"}, "taxable_amount": {"amount_e5": 700000, "currency_code": "TWD", "formatted": "NT$7.00"}, "tax_remittance": "MERCHANT"}]}], "fees": [{"instance_id": "ff1f7347-b239-4bf2-9170-fdf8ed1abe63", "description": "DELIVERY_FEE", "gross_amount": {"amount_e5": 5500000, "currency_code": "TWD", "formatted": "NT$55.00"}, "net_amount": {"amount_e5": 5238100, "currency_code": "TWD", "formatted": "NT$52.38"}, "total_tax": {"amount_e5": 261900, "currency_code": "TWD", "formatted": "NT$2.62"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 261900, "currency_code": "TWD", "formatted": "NT$2.62"}}]}, {"instance_id": "ff1f7347-b239-4bf2-9170-fdf8ed1abe63", "description": "SERVICE_FEE", "gross_amount": {"amount_e5": 500000, "currency_code": "TWD", "formatted": "NT$5.00"}, "net_amount": {"amount_e5": 476190, "currency_code": "TWD", "formatted": "NT$4.76"}, "total_tax": {"amount_e5": 23810, "currency_code": "TWD", "formatted": "NT$0.24"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 23810, "currency_code": "TWD", "formatted": "NT$0.24"}}]}]}}, "tender_types": null}, "status": "ACTIVE", "preparation_status": "PREPARING", "estimated_unfulfilled_at": "2024-03-12T21:00:30+08:00", "is_order_accuracy_risk": false, "preparation_time": {"ready_for_pickup_time_secs": 900, "source": "DEFAULT", "ready_for_pickup_time": "2024-03-12T21:04:07+08:00"}, "action_eligibility": {"adjust_ready_for_pickup_time": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "mark_out_of_item": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "cancel": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "mark_cannot_fulfill": {"is_eligible": false, "reason": "NO_ORDER_INSTRUCTIONS"}}, "fulfillment_type": "DELIVERY_BY_UBER", "created_time": "2024-03-12T20:49:00+08:00", "has_membership_pass": false}
{"id": "a0612fe6-74bc-4280-b9b7-ed90712f9c52", "display_id": "F9C52", "state": "OFFERED", "ordering_platform": "UBER_EATS", "store": {"id": "d4668854-50c2-40c7-8ad0-5af53b28ed0a", "name": "逸之牛 Steak & Yakiniku 高雄創始店", "timezone": "Asia/Taipei", "partner_identifiers": [{"value": "d4668854-50c2-40c7-8ad0-5af53b28ed0a", "type": "MERCHANT_STORE_ID"}, {"value": "T_HzBzABVuTUtm1QgtG9bKMHXFtUk0UP", "type": "ORDER_MANAGER_CLIENT_ID"}, {"value": "Perkd Store", "type": "INTEGRATOR_STORE_ID"}, {"value": "Perkd", "type": "INTEGRATOR_BRAND_ID"}]}, "customers": [{"id": "32dc3ac9-26e2-5e99-a458-4688a7691a38", "name": {"display_name": "端文 謝.", "first_name": "端文", "last_name": "謝."}, "order_history": {"past_order_count": 5}, "is_primary_customer": true, "contact": {"phone": {"country_iso2": "TW", "number": "+886 2 5594 1277", "pin_code": "150 70 945"}}, "can_respond_to_fulfillment_issues": true}], "deliveries": [{"id": "fef2f4b4-a98f-4713-a571-2145278886ba", "delivery_partner": {"id": "4822bd14-4415-5a97-a8c1-15ad3cc1756a", "name": {"display_name": "錦達"}, "picture_url": "https://d1w2poirtb3as9.cloudfront.net/318768d0b798cf54929f.jpeg?Expires=**********&Signature=eybivV1KhpJ44tci-E88qFsFomiwDw5OzDsOeveskxVl1G-1s--Fr4TXqZGTwyHj4M0cMEkOuxRywlzKNiZ8-x25AYNufMQxZFF1kZNiliCcLo4LK8ugJYR9Qa4BDrOqlV5Ion3mgvcrmr-PGbnA1JF-E-6ce7pJpCuEAIxbvgDEQZz6WqTx74OzRoaOKwijYbUZDFCtLbNYknyJ~hZqXIOiNTAO9uKyVMhnxmFGaFWuGP-ycbus5dSAWb7YRjeTGh7WAFS9-M0Hq1k3BNBnA1vnLcHjT1F1j70bQojRSrJHrTFdvmm2LwKTveHVMXMmasrTPGy9pd9hLR8gW5TCww__&Key-Pair-Id=K36LFL06Z5BT10", "vehicle": {"license_plate": "568-NVM", "model": "Motorbike", "make": "Uber", "type": "UNKNOWN", "color": "darkblue", "is_autonomous": false}, "contact": {"phone": {"country_iso2": "TW", "number": "+886255941277", "pin_code": "36325162"}}, "current_location": {"latitude": "22.630997592131088", "longitude": "120.30032405157773"}}, "status": "EN_ROUTE_TO_PICKUP", "estimated_pick_up_time": "2024-08-22T12:11:25+08:00", "interaction_type": "DELIVER_TO_DOOR", "estimated_drop_off_time": "2024-08-22T12:21:38+08:00", "instructions": "不用按鈴，直接進來放桌上，謝謝"}], "carts": [{"id": "a80067d6-f8cb-481d-a0e0-806ef8b588ba", "revision_id": "59353491-a244-54ca-9e88-71f922481f61", "items": [{"id": "669566f31be83528b8ffe8e1", "cart_item_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "customer_id": "32dc3ac9-26e2-5e99-a458-4688a7691a38", "external_data": "gxzz-2", "title": "炙燒雪花豚丼(不含蛋) Broiled Marbled Pork Rice Bowl", "quantity": {"amount": 2, "unit": "PIECE"}, "picture_url": "https://tb-static.uber.com/prod/image-proc/processed_images/12431ddc5d9a0a23bd4ef284f26aad38/5143f1e218c67c20fe5a4cd33d90b07b.jpeg"}], "special_instructions": "少醬", "include_single_use_items": false, "restricted_items": {"alcohol": {"contain_alcoholic_item": false}}}], "payment": {"payment_detail": {"order_total": {"net": {"amount_e5": 15700000, "currency_code": "TWD", "formatted": "$157.00"}, "tax": {"amount_e5": 800000, "currency_code": "TWD", "formatted": "$8.00"}, "gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "$165.00"}, "display_amount": "$165.00", "is_tax_inclusive": true}, "item_charges": {"total": {"net": {"amount_e5": 31400000, "currency_code": "TWD", "formatted": "$314.00"}, "tax": {"amount_e5": 1600000, "currency_code": "TWD", "formatted": "$16.00"}, "gross": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "display_amount": "$330.00", "is_tax_inclusive": true}, "price_breakdown": [{"cart_item_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "price_type": "ITEM", "quantity": {"amount": 2, "unit": "PIECE"}, "total": {"gross": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "display_amount": "$330.00"}, "discount": {"total": {"gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "$165.00"}, "display_amount": "$165.00"}, "quantity": {"amount": 1, "unit": "PIECE"}}, "unit": {"gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "$165.00"}, "display_amount": "$165.00"}}], "subtotal_including_promos": {"net": {"amount_e5": 15700000, "currency_code": "TWD", "formatted": "NT$157.00"}, "tax": {"amount_e5": 800000, "currency_code": "TWD", "formatted": "NT$8.00"}, "gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "NT$165.00"}, "display_amount": "NT$165.00", "is_tax_inclusive": true}}, "fees": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "display_amount": "$0.00", "is_tax_inclusive": true}}, "promotions": {"total": {"net": {"amount_e5": -15700000, "currency_code": "TWD", "formatted": "-NT$157.00"}, "tax": {"amount_e5": -800000, "currency_code": "TWD", "formatted": "-$8.00"}, "gross": {"amount_e5": -16500000, "currency_code": "TWD", "formatted": "-NT$165.00"}, "display_amount": "-NT$165.00", "is_tax_inclusive": true}, "details": [{"type": "BOGO", "discount_value": "-16500", "discount_percentage": "0", "discount_items": [{"external_id": "669566f31be83528b8ffe8e1", "discounted_quantity": 1, "discount_amount_applied": -16500}]}], "order_total_excluding_promos": {"net": {"amount_e5": 31400000, "currency_code": "TWD", "formatted": "$314.00"}, "tax": {"amount_e5": 1600000, "currency_code": "TWD", "formatted": "$16.00"}, "gross": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "display_amount": "$330.00", "is_tax_inclusive": true}}, "currency_code": "TWD", "marketplace_fee_due_to_uber": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "display_amount": "$0.00", "is_tax_inclusive": true}}, "tax_reporting": {"breakdown": {"items": [{"instance_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "description": "ITEM", "gross_amount": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "net_amount": {"amount_e5": 31428571, "currency_code": "TWD", "formatted": "$314.29"}, "total_tax": {"amount_e5": 1571428, "currency_code": "TWD", "formatted": "$15.71"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 1571428, "currency_code": "TWD", "formatted": "$15.71"}, "taxable_amount": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "tax_remittance": "MERCHANT"}]}], "fees": [{"instance_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "description": "SERVICE_FEE", "gross_amount": {"amount_e5": 1700000, "currency_code": "TWD", "formatted": "$17.00"}, "net_amount": {"amount_e5": 1619050, "currency_code": "TWD", "formatted": "$16.19"}, "total_tax": {"amount_e5": 80950, "currency_code": "TWD", "formatted": "$0.81"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 80950, "currency_code": "TWD", "formatted": "$0.81"}}]}, {"instance_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "description": "DELIVERY_FEE", "gross_amount": {"amount_e5": 1000000, "currency_code": "TWD", "formatted": "$10.00"}, "net_amount": {"amount_e5": 952380, "currency_code": "TWD", "formatted": "$9.52"}, "total_tax": {"amount_e5": 47620, "currency_code": "TWD", "formatted": "$0.48"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 47620, "currency_code": "TWD", "formatted": "$0.48"}}]}], "promotions": [{"instance_id": "a95bfd4f-d047-4039-8691-7849b2307c41", "description": "ITEM_PROMOTION", "gross_amount": {"amount_e5": -16500000, "currency_code": "TWD", "formatted": "-$165.00"}, "net_amount": {"amount_e5": -15714285, "currency_code": "TWD", "formatted": "-$157.14"}, "total_tax": {"amount_e5": -785714, "currency_code": "TWD", "formatted": "-$7.86"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": -785714, "currency_code": "TWD", "formatted": "-$7.86"}}]}]}}, "tender_types": null}, "status": "ACTIVE", "estimated_unfulfilled_at": "2024-08-22T12:19:28+08:00", "is_order_accuracy_risk": false, "store_instructions": "少醬", "preparation_time": {"ready_for_pickup_time_secs": 420, "source": "PREDICTED_BY_UBER", "ready_for_pickup_time": "2024-08-22T12:14:58+08:00"}, "action_eligibility": {"adjust_ready_for_pickup_time": {"is_eligible": false, "reason": "COURIER_DISPATCHED"}, "mark_out_of_item": {"is_eligible": false, "reason": "COURIER_DISPATCHED"}, "cancel": {"is_eligible": false, "reason": "COURIER_DISPATCHED"}, "mark_cannot_fulfill": {"is_eligible": false, "reason": "COURIER_DISPATCHED"}}, "fulfillment_type": "DELIVERY_BY_UBER", "created_time": "2024-08-22T12:07:58+08:00", "has_membership_pass": true}
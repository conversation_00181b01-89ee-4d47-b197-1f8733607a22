{"id": "7e1d418f-9b27-4897-b53b-ae937573abfc", "display_id": "3ABFC", "state": "OFFERED", "ordering_platform": "UBER_EATS", "store": {"id": "d4668854-50c2-40c7-8ad0-5af53b28ed0a", "name": "逸之牛 Steak & Yakiniku 高雄創始店", "timezone": "Asia/Taipei", "partner_identifiers": [{"value": "d4668854-50c2-40c7-8ad0-5af53b28ed0a", "type": "MERCHANT_STORE_ID"}, {"value": "T_HzBzABVuTUtm1QgtG9bKMHXFtUk0UP", "type": "ORDER_MANAGER_CLIENT_ID"}, {"value": "Perkd Store", "type": "INTEGRATOR_STORE_ID"}, {"value": "Perkd", "type": "INTEGRATOR_BRAND_ID"}]}, "customers": [{"id": "786c10cc-d04b-5341-be92-05f6afa6b11b", "name": {"display_name": "雅 莊.", "first_name": "雅", "last_name": "莊."}, "order_history": {"past_order_count": 0}, "is_primary_customer": true, "contact": {"phone": {"country_iso2": "TW", "number": "+886 2 5594 1277", "pin_code": "707 92 868"}}, "can_respond_to_fulfillment_issues": true}], "carts": [{"id": "e586bf18-c05a-4ab5-a7ea-edca6d643cb1", "revision_id": "792ceefb-99f6-547d-b0db-2a80a48bf730", "items": [{"id": "668b566c1be83528b8d99df6", "cart_item_id": "02f9a226-4a88-4396-9ac1-0950da5f375e", "customer_id": "786c10cc-d04b-5341-be92-05f6afa6b11b", "external_data": "gxzz-1", "title": "炙燒雪花牛丼(不含蛋) Broiled Marbled Beef Rice Bowl", "quantity": {"amount": 2, "unit": "PIECE"}, "picture_url": "https://tb-static.uber.com/prod/image-proc/processed_images/9f817c14d600346c0757b04b5d26d6d7/5143f1e218c67c20fe5a4cd33d90b07b.jpeg"}, {"id": "669566f31be83528b8ffe8e1", "cart_item_id": "1deb8fa1-f087-46b9-95f1-4ed34d62e22e", "customer_id": "786c10cc-d04b-5341-be92-05f6afa6b11b", "external_data": "gxzz-2", "title": "炙燒雪花豚丼(不含蛋) Broiled Marbled Pork Rice Bowl", "quantity": {"amount": 2, "unit": "PIECE"}, "picture_url": "https://tb-static.uber.com/prod/image-proc/processed_images/12431ddc5d9a0a23bd4ef284f26aad38/5143f1e218c67c20fe5a4cd33d90b07b.jpeg"}], "include_single_use_items": false, "restricted_items": {"alcohol": {"contain_alcoholic_item": false}}}], "payment": {"payment_detail": {"order_total": {"net": {"amount_e5": 32400000, "currency_code": "TWD", "formatted": "$324.00"}, "tax": {"amount_e5": 1600000, "currency_code": "TWD", "formatted": "$16.00"}, "gross": {"amount_e5": 34000000, "currency_code": "TWD", "formatted": "$340.00"}, "display_amount": "$340.00", "is_tax_inclusive": true}, "item_charges": {"total": {"net": {"amount_e5": 64800000, "currency_code": "TWD", "formatted": "$648.00"}, "tax": {"amount_e5": 3200000, "currency_code": "TWD", "formatted": "$32.00"}, "gross": {"amount_e5": 68000000, "currency_code": "TWD", "formatted": "$680.00"}, "display_amount": "$680.00", "is_tax_inclusive": true}, "price_breakdown": [{"cart_item_id": "02f9a226-4a88-4396-9ac1-0950da5f375e", "price_type": "ITEM", "quantity": {"amount": 2, "unit": "PIECE"}, "total": {"gross": {"amount_e5": 35000000, "currency_code": "TWD", "formatted": "$350.00"}, "display_amount": "$350.00"}, "discount": {"total": {"gross": {"amount_e5": 17500000, "currency_code": "TWD", "formatted": "$175.00"}, "display_amount": "$175.00"}, "quantity": {"amount": 1, "unit": "PIECE"}}, "unit": {"gross": {"amount_e5": 17500000, "currency_code": "TWD", "formatted": "$175.00"}, "display_amount": "$175.00"}}, {"cart_item_id": "1deb8fa1-f087-46b9-95f1-4ed34d62e22e", "price_type": "ITEM", "quantity": {"amount": 2, "unit": "PIECE"}, "total": {"gross": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "display_amount": "$330.00"}, "discount": {"total": {"gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "$165.00"}, "display_amount": "$165.00"}, "quantity": {"amount": 1, "unit": "PIECE"}}, "unit": {"gross": {"amount_e5": 16500000, "currency_code": "TWD", "formatted": "$165.00"}, "display_amount": "$165.00"}}], "subtotal_including_promos": {"net": {"amount_e5": 32400000, "currency_code": "TWD", "formatted": "NT$324.00"}, "tax": {"amount_e5": 1600000, "currency_code": "TWD", "formatted": "NT$16.00"}, "gross": {"amount_e5": 34000000, "currency_code": "TWD", "formatted": "NT$340.00"}, "display_amount": "NT$340.00", "is_tax_inclusive": true}}, "fees": {"total": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "display_amount": "$0.00", "is_tax_inclusive": true}}, "promotions": {"total": {"net": {"amount_e5": -32400000, "currency_code": "TWD", "formatted": "-NT$324.00"}, "tax": {"amount_e5": -1600000, "currency_code": "TWD", "formatted": "-$16.00"}, "gross": {"amount_e5": -34000000, "currency_code": "TWD", "formatted": "-NT$340.00"}, "display_amount": "-NT$340.00", "is_tax_inclusive": true}, "details": [{"type": "BOGO", "discount_value": "-34000", "discount_percentage": "0", "discount_items": [{"external_id": "668b566c1be83528b8d99df6", "discounted_quantity": 1, "discount_amount_applied": -17500}, {"external_id": "669566f31be83528b8ffe8e1", "discounted_quantity": 1, "discount_amount_applied": -16500}]}], "order_total_excluding_promos": {"net": {"amount_e5": 64800000, "currency_code": "TWD", "formatted": "$648.00"}, "tax": {"amount_e5": 3200000, "currency_code": "TWD", "formatted": "$32.00"}, "gross": {"amount_e5": 68000000, "currency_code": "TWD", "formatted": "$680.00"}, "display_amount": "$680.00", "is_tax_inclusive": true}}, "currency_code": "TWD", "marketplace_fee_due_to_uber": {"net": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "tax": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "gross": {"amount_e5": 0, "currency_code": "TWD", "formatted": "$0.00"}, "display_amount": "$0.00", "is_tax_inclusive": true}}, "tax_reporting": {"breakdown": {"items": [{"instance_id": "02f9a226-4a88-4396-9ac1-0950da5f375e", "description": "ITEM", "gross_amount": {"amount_e5": 35000000, "currency_code": "TWD", "formatted": "$350.00"}, "net_amount": {"amount_e5": 33333333, "currency_code": "TWD", "formatted": "$333.33"}, "total_tax": {"amount_e5": 1666666, "currency_code": "TWD", "formatted": "$16.67"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 1666666, "currency_code": "TWD", "formatted": "$16.67"}, "taxable_amount": {"amount_e5": 35000000, "currency_code": "TWD", "formatted": "$350.00"}, "tax_remittance": "MERCHANT"}]}, {"instance_id": "1deb8fa1-f087-46b9-95f1-4ed34d62e22e", "description": "ITEM", "gross_amount": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "net_amount": {"amount_e5": 31428571, "currency_code": "TWD", "formatted": "$314.29"}, "total_tax": {"amount_e5": 1571428, "currency_code": "TWD", "formatted": "$15.71"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": 1571428, "currency_code": "TWD", "formatted": "$15.71"}, "taxable_amount": {"amount_e5": 33000000, "currency_code": "TWD", "formatted": "$330.00"}, "tax_remittance": "MERCHANT"}]}], "promotions": [{"instance_id": "02f9a226-4a88-4396-9ac1-0950da5f375e", "description": "ITEM_PROMOTION", "gross_amount": {"amount_e5": -17500000, "currency_code": "TWD", "formatted": "-$175.00"}, "net_amount": {"amount_e5": -16666666, "currency_code": "TWD", "formatted": "-$166.67"}, "total_tax": {"amount_e5": -833333, "currency_code": "TWD", "formatted": "-$8.33"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": -833333, "currency_code": "TWD", "formatted": "-$8.33"}}]}, {"instance_id": "1deb8fa1-f087-46b9-95f1-4ed34d62e22e", "description": "ITEM_PROMOTION", "gross_amount": {"amount_e5": -16500000, "currency_code": "TWD", "formatted": "-$165.00"}, "net_amount": {"amount_e5": -15714285, "currency_code": "TWD", "formatted": "-$157.14"}, "total_tax": {"amount_e5": -785714, "currency_code": "TWD", "formatted": "-$7.86"}, "taxes": [{"is_inclusive": true, "rate": "0.05", "calculated_tax": {"amount_e5": -785714, "currency_code": "TWD", "formatted": "-$7.86"}}]}]}}, "tender_types": null}, "status": "ACTIVE", "estimated_unfulfilled_at": "2024-08-22T12:34:23+08:00", "is_order_accuracy_risk": false, "preparation_time": {"ready_for_pickup_time_secs": 420, "source": "PREDICTED_BY_UBER", "ready_for_pickup_time": "2024-08-22T12:29:53+08:00"}, "action_eligibility": {"adjust_ready_for_pickup_time": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "mark_out_of_item": {"is_eligible": true, "reason": "ORDER_VALID_STATE"}, "cancel": {"is_eligible": false, "reason": "ORDER_NOT_ACCEPTED"}, "mark_cannot_fulfill": {"is_eligible": false, "reason": "NO_ORDER_INSTRUCTIONS"}}, "fulfillment_type": "PICKUP", "created_time": "2024-08-22T12:22:53+08:00", "has_membership_pass": false}